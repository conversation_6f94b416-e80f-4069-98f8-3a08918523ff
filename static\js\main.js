/**
 * 内容审核管家 - 主应用逻辑
 * 纯前端版本
 */

/**
 * 文件验证器类
 */
class FileValidator {
    static validateImageFile(file, config) {
        this.validateFileType(file, config.imageProcessing.supportedFormats);
        this.validateFileSize(file, config.imageProcessing.maxSize);
        return true;
    }

    static validateTextFile(file, config) {
        this.validateFileType(file, config.textProcessing.supportedFormats);
        this.validateFileSize(file, config.textProcessing.maxSize || 10 * 1024 * 1024); // 默认10MB
        return true;
    }

    static validateFileType(file, supportedFormats) {
        const fileExtension = file.name.split('.').pop().toLowerCase();
        if (!supportedFormats.includes(fileExtension)) {
            throw new Error(`不支持的文件格式: ${fileExtension}`);
        }
    }

    static validateFileSize(file, maxSize) {
        if (file.size > maxSize) {
            const maxSizeMB = Math.round(maxSize / (1024 * 1024));
            throw new Error(`文件过大，请选择小于${maxSizeMB}MB的文件`);
        }
    }
}

/**
 * 图片处理器类
 */
class ImageProcessor {
    static async generatePreview(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(new Error('图片读取失败'));
            reader.readAsDataURL(file);
        });
    }

    static async convertToBase64(file) {
        return this.generatePreview(file); // 同样的逻辑
    }

    static formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

/**
 * 文本处理器类
 */
class TextProcessor {
    static async readTextFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(new Error('文本文件读取失败'));
            reader.readAsText(file, 'UTF-8');
        });
    }
}

/**
 * 进度管理器类
 */
class ProgressManager {
    constructor(container) {
        this.container = container;
        this.activeProgresses = new Map();
        this.progressCounter = 0;
    }

    /**
     * 显示进度条
     */
    show(type, customSteps = null) {
        const progressId = `progress_${++this.progressCounter}`;
        const steps = customSteps || this.getDefaultSteps(type);
        const progressElement = this.createProgressElement(progressId, type, steps);

        this.container.appendChild(progressElement);
        this.activeProgresses.set(progressId, {
            element: progressElement,
            steps: steps,
            startTime: Date.now()
        });

        // 确保进度卡片立即可见
        progressElement.style.opacity = '1';
        progressElement.style.transform = 'translateY(0)';
        progressElement.classList.add('fade-in');

        // 自动滚动到底部
        this.container.scrollTop = this.container.scrollHeight;

        // 强制重新渲染
        this.container.offsetHeight;

        // 现在元素已经在DOM中，启动计时器
        this.startTimer(progressId);

        console.log('📋 进度卡片已添加到DOM，元素:', progressElement);
        console.log('📋 容器子元素数量:', this.container.children.length);

        return progressId;
    }

    /**
     * 更新步骤状态
     */
    updateStep(progressId, stepIndex, status) {
        const progress = this.activeProgresses.get(progressId);
        if (!progress || stepIndex >= progress.steps.length) return;

        const stepElement = progress.element.querySelector(`[data-step="${stepIndex}"]`);
        if (stepElement) {
            stepElement.className = `analysis-step ${status}`;

            if (status === 'completed') {
                stepElement.querySelector('.step-icon').textContent = '✅';
            } else if (status === 'active') {
                stepElement.querySelector('.step-icon').textContent = '⏳';
            }
        }
    }

    /**
     * 移除进度条
     */
    remove(progressId) {
        const progress = this.activeProgresses.get(progressId);
        if (progress) {
            // 先停止计时器
            this.stopTimer(progressId);

            // 显示完成状态，延长显示时间让用户看到完成效果
            progress.element.classList.add('analysis-completed');

            // 更新所有步骤为完成状态
            const steps = progress.element.querySelectorAll('.analysis-step');
            steps.forEach(step => {
                step.classList.remove('pending', 'active');
                step.classList.add('completed');
                const icon = step.querySelector('.step-icon');
                if (icon) {
                    icon.textContent = '✅';
                }
            });

            // 更新标题显示完成状态
            const header = progress.element.querySelector('.analysis-header h4');
            if (header) {
                header.innerHTML = header.innerHTML.replace(/分析/, '分析完成');
            }

            // 延迟移除，让用户看到完成状态
            setTimeout(() => {
                if (progress.element.parentNode) {
                    progress.element.style.transition = 'opacity 0.5s ease-out';
                    progress.element.style.opacity = '0';
                    setTimeout(() => {
                        if (progress.element.parentNode) {
                            progress.element.remove();
                        }
                    }, 500);
                }
            }, 2000); // 延长到2秒，让用户看到完成状态

            this.activeProgresses.delete(progressId);
        }
    }

    /**
     * 获取默认步骤
     */
    getDefaultSteps(type) {
        const stepTemplates = {
            text: ['本地关键词检测', 'AI智能分析', '结果整理生成'],
            image: ['图片内容识别', 'AI智能分析', '结果生成报告'],
            imageText: ['图片内容识别', '文案内容分析', '综合分析生成']
        };
        return stepTemplates[type] || stepTemplates.text;
    }

    /**
     * 创建进度元素
     */
    createProgressElement(progressId, type, steps) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant';
        messageDiv.id = progressId;

        const typeLabels = {
            text: '📝 文案分析',
            image: '🖼️ 图片分析',
            imageText: '📄 图文分析'
        };

        const stepsHtml = steps.map((step, index) => `
            <div class="analysis-step pending" data-step="${index}">
                <span class="step-icon">⏸️</span>
                <span class="step-text">${step}</span>
            </div>
        `).join('');

        messageDiv.innerHTML = `
            <div class="message-avatar">🔍</div>
            <div class="message-content">
                <div class="message-text">
                    <div class="analysis-progress">
                        <div class="analysis-header">
                            <h4>🔍 ${typeLabels[type] || '正在分析您的内容'}</h4>
                            <div class="progress-circle"></div>
                            <div class="analysis-timer" id="timer_${progressId}">00:00</div>
                        </div>
                        <div class="analysis-steps">
                            ${stepsHtml}
                        </div>
                    </div>
                </div>
                <div class="message-time">${new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}</div>
            </div>
        `;

        return messageDiv;
    }

    /**
     * 启动计时器
     */
    startTimer(progressId) {
        const timerElement = document.getElementById(`timer_${progressId}`);
        if (!timerElement) return;

        const progress = this.activeProgresses.get(progressId);
        if (!progress) return;

        const updateTimer = () => {
            const elapsed = Math.floor((Date.now() - progress.startTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        };

        // 使用内存管理器注册定时器
        if (window.memoryManager) {
            progress.timerInterval = window.memoryManager.registerInterval(setInterval(updateTimer, 1000));
        } else {
            progress.timerInterval = setInterval(updateTimer, 1000);
        }
        updateTimer(); // 立即更新一次
    }

    /**
     * 停止计时器
     */
    stopTimer(progressId) {
        const progress = this.activeProgresses.get(progressId);
        if (progress && progress.timerInterval) {
            // 使用内存管理器清理定时器
            if (window.memoryManager) {
                window.memoryManager.clearInterval(progress.timerInterval);
            } else {
                clearInterval(progress.timerInterval);
            }
            delete progress.timerInterval;
        }
    }

    /**
     * 清理所有进度
     */
    clearAll() {
        for (const [progressId] of this.activeProgresses) {
            this.remove(progressId);
        }
    }

    /**
     * 销毁进度管理器
     */
    destroy() {
        // 清理所有活跃进度
        for (const [progressId] of this.activeProgresses) {
            this.stopTimer(progressId);
            const progress = this.activeProgresses.get(progressId);
            if (progress && progress.element && progress.element.parentNode) {
                progress.element.remove();
            }
        }
        this.activeProgresses.clear();
    }
}

class ContentAuditorApp {
    constructor() {
        this.config = window.ContentAuditorConfig;
        this.api = window.contentAuditorAPI;
        this.currentTab = 'text';
        this.conversationHistory = [];
        this.currentImage = null;
        this.isProcessing = false;
        this.currentMode = 'intelligent'; // 当前模式：intelligent 或 chat
        this.deepThinkingEnabled = true; // 深度思考模式状态（仅对话模式有效）
        this.isRecording = false;
        this.recognition = null;
        this.memoryManager = window.memoryManager; // 内存管理器引用
        this.isDestroyed = false; // 销毁状态标记

        this.init();
    }

    /**
     * 初始化应用
     */
    async init() {
        this.initializeElements();
        this.bindEvents();
        this.loadTheme();
        this.hideLoadingOverlay();

        // 初始化进度管理器
        this.progressManager = new ProgressManager(this.elements.chatMessages);

        // 等待API初始化完成
        this.api = await window.waitForAPIReady();
        this.checkApiKey();

        // 加载深度思考状态
        this.loadDeepThinkingState();

        // 显示初始欢迎消息
        this.showInitialWelcome();
    }

    /**
     * 显示初始欢迎消息
     */
    showInitialWelcome() {
        // 先加载对话历史
        this.loadConversationHistory();

        // 如果没有对话历史，显示欢迎消息
        if (this.conversationHistory.length === 0) {
            // 根据当前模式显示对应的欢迎消息
            if (this.currentMode === 'intelligent') {
                this.showIntelligentWelcome();
            } else {
                this.showChatWelcome();
            }
        }
    }

    /**
     * 初始化DOM元素
     */
    initializeElements() {
        // 主要元素
        this.elements = {
            // 标签页
            tabButtons: document.querySelectorAll('.tab-btn'),
            tabContents: document.querySelectorAll('.tab-content'),

            // 文案相关
            textInput: document.getElementById('text-input'),
            textPreview: document.getElementById('text-preview'),
            previewContent: document.getElementById('preview-content'),
            analyzeTextBtn: document.getElementById('analyze-text'),
            uploadDocumentBtn: document.getElementById('upload-document'),
            documentInput: document.getElementById('document-input'),
            clearTextBtn: document.getElementById('clear-text'),
            charCount: document.querySelector('.char-count'),

            // 图片相关
            uploadArea: document.getElementById('upload-area'),
            fileInput: document.getElementById('file-input'),
            imagePreview: document.getElementById('image-preview'),
            previewImage: document.getElementById('preview-image'),
            removeImageBtn: document.getElementById('remove-image'),
            imageName: document.getElementById('image-name'),
            imageSize: document.getElementById('image-size'),
            imageTextInput: document.getElementById('image-text-input'),
            imageTextGroup: document.getElementById('image-text-group'),
            imageButtons: document.getElementById('image-buttons'),
            analyzeImageBtn: document.getElementById('analyze-image'),
            analyzeImageTextBtn: document.getElementById('analyze-image-text'),

            // 聊天相关
            chatMessages: document.getElementById('chat-messages'),
            chatInput: document.getElementById('chat-input'),
            sendBtn: document.getElementById('send-btn'),
            voiceInputBtn: document.getElementById('voice-input-chat'),
            quickButtons: document.querySelectorAll('.quick-btn'),
            modeLabels: document.querySelectorAll('.mode-label'),

            // 其他
            themeToggle: document.getElementById('theme-toggle'),
            helpBtn: document.getElementById('help-btn'),
            helpModal: document.getElementById('help-modal'),
            closeHelp: document.getElementById('close-help'),
            roadmapBtn: document.getElementById('roadmap-btn'),
            roadmapModal: document.getElementById('roadmap-modal'),
            closeRoadmap: document.getElementById('close-roadmap'),
            toast: document.getElementById('toast'),
            loadingOverlay: document.getElementById('loading-overlay')
        };
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 标签页切换
        this.elements.tabButtons.forEach(btn => {
            btn.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
        });

        // 文案相关事件
        this.elements.textInput.addEventListener('input', () => this.updateCharCount());
        this.elements.textInput.addEventListener('input', () => this.updatePreview());
        this.elements.analyzeTextBtn.addEventListener('click', () => this.handleAnalyzeText());
        this.elements.uploadDocumentBtn.addEventListener('click', () => this.elements.documentInput.click());
        this.elements.documentInput.addEventListener('change', (e) => this.handleDocumentUpload(e));
        this.elements.clearTextBtn.addEventListener('click', () => this.clearText());

        // 图片相关事件
        this.elements.uploadArea.addEventListener('click', () => this.elements.fileInput.click());
        this.elements.uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.elements.uploadArea.addEventListener('drop', (e) => this.handleDrop(e));
        this.elements.fileInput.addEventListener('change', (e) => this.handleImageUpload(e));
        this.elements.removeImageBtn.addEventListener('click', () => this.removeImage());
        this.elements.analyzeImageBtn.addEventListener('click', () => this.handleAnalyzeImage());
        this.elements.analyzeImageTextBtn.addEventListener('click', () => this.handleAnalyzeImageText());

        // 图片文案输入监听
        this.elements.imageTextInput.addEventListener('input', () => this.updateImageAnalysisButtons());

        // 聊天相关事件
        this.elements.chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        this.elements.sendBtn.addEventListener('click', () => this.sendMessage());
        this.elements.voiceInputBtn.addEventListener('click', () => this.toggleVoiceInput());
        this.elements.quickButtons.forEach(btn => {
            btn.addEventListener('click', (e) => this.handleQuickAction(e.target.dataset.action));
        });

        // 模式切换事件
        this.elements.modeLabels.forEach(label => {
            label.addEventListener('click', (e) => this.switchMode(e.target.dataset.mode));
        });

        // 其他事件
        this.elements.themeToggle.addEventListener('click', () => this.toggleTheme());
        this.elements.helpBtn.addEventListener('click', () => this.showHelp());
        this.elements.closeHelp.addEventListener('click', () => this.hideHelp());
        this.elements.roadmapBtn.addEventListener('click', () => this.showRoadmap());
        this.elements.closeRoadmap.addEventListener('click', () => this.hideRoadmap());

        // 模态框外部点击关闭
        this.elements.helpModal.addEventListener('click', (e) => {
            if (e.target === this.elements.helpModal) {
                this.hideHelp();
            }
        });

        this.elements.roadmapModal.addEventListener('click', (e) => {
            if (e.target === this.elements.roadmapModal) {
                this.hideRoadmap();
            }
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
    }

    /**
     * 切换标签页
     */
    switchTab(tabName) {
        this.currentTab = tabName;

        // 更新标签按钮状态
        this.elements.tabButtons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tabName);
        });

        // 更新标签内容显示
        this.elements.tabContents.forEach(content => {
            content.classList.toggle('active', content.id === `${tabName}-tab`);
        });
    }

    /**
     * 切换模式（智能体模式 / 对话模式）
     */
    switchMode(mode) {
        // 如果模式没有变化，直接返回
        if (this.currentMode === mode) {
            return;
        }

        this.currentMode = mode;

        // 更新模式标签状态
        this.elements.modeLabels.forEach(label => {
            label.classList.toggle('active', label.dataset.mode === mode);
        });

        // 更新界面显示
        const quickActions = document.querySelector('.quick-actions');
        const chatTitle = document.querySelector('.chat-title .icon');
        const chatTitleText = document.querySelector('.chat-title');

        if (mode === 'intelligent') {
            // 智能体模式
            quickActions.style.display = 'block';
            chatTitle.textContent = '🤖';
            chatTitleText.innerHTML = '<span class="icon">🤖</span>AI智能助手';
        } else {
            // 对话模式
            quickActions.style.display = 'none';
            chatTitle.textContent = '💬';
            chatTitleText.innerHTML = '<span class="icon">💬</span>对话助手';
        }

        // 只有在没有对话历史时才显示欢迎消息
        const hasMessages = this.elements.chatMessages.children.length > 0;
        if (!hasMessages) {
            if (mode === 'intelligent') {
                this.showIntelligentWelcome();
            } else {
                this.showChatWelcome();
            }
        } else {
            // 如果有消息，强制刷新显示
            this.forceRefreshChatDisplay();
        }

        this.showToast(`已切换到${mode === 'intelligent' ? '智能体' : '对话'}模式`, 'success');
    }

    /**
     * 更新模式界面状态（不显示提示）
     */
    updateModeUI() {
        // 更新模式标签状态
        this.elements.modeLabels.forEach(label => {
            label.classList.toggle('active', label.dataset.mode === this.currentMode);
        });

        // 更新界面显示
        const quickActions = document.querySelector('.quick-actions');
        const chatTitle = document.querySelector('.chat-title .icon');
        const chatTitleText = document.querySelector('.chat-title');
        const thinkingToggleBtn = document.getElementById('thinking-toggle-btn');

        if (this.currentMode === 'intelligent') {
            // 智能体模式
            quickActions.style.display = 'block';
            thinkingToggleBtn.style.display = 'none';
            chatTitle.textContent = '🤖';
            chatTitleText.innerHTML = '<span class="icon">🤖</span>AI智能助手';
        } else {
            // 对话模式
            quickActions.style.display = 'none';
            thinkingToggleBtn.style.display = 'inline-flex';
            chatTitle.textContent = '💬';
            chatTitleText.innerHTML = '<span class="icon">💬</span>对话助手';

            // 更新深度思考按钮状态
            this.updateThinkingButtonState();
        }
    }

    /**
     * 显示智能体模式欢迎消息
     */
    showIntelligentWelcome() {
        const welcomeContent = `
            👋 <strong>欢迎使用内容审核管家智能体模式！</strong><br><br>
            🎯 我是您的专业内容合规审核助手，提供<strong>句子级精确定位</strong>和<strong>四色风险标记</strong>。<br>
            📝 请在左侧输入文案或上传图片，点击分析按钮开始审核，也可使用下方快捷指令获得专业建议。<br>
            🚀 让我们开始为您的内容保驾护航！
        `;

        // 清空聊天区域并添加欢迎消息
        this.elements.chatMessages.innerHTML = '';
        this.addMessage('assistant', welcomeContent);
    }

    /**
     * 显示对话模式欢迎消息
     */
    showChatWelcome() {
        const welcomeContent = `
            👋 <strong>欢迎使用对话模式！</strong><br><br>
            💬 在这个模式下，您可以与我进行<strong>自由对话</strong>，咨询任何关于内容合规的问题。<br>
            📚 我支持<strong>多轮深度对话</strong>，适合学习广告法规知识和讨论具体案例。<br>
            ❓ 请随时向我提问，我会尽力为您提供专业的帮助！
        `;

        // 清空聊天区域并添加欢迎消息
        this.elements.chatMessages.innerHTML = '';
        this.addMessage('assistant', welcomeContent);
    }

    /**
     * 更新字符计数
     */
    updateCharCount() {
        const text = this.elements.textInput.value;
        const count = text.length;
        const maxLength = this.config.textProcessing.maxLength;

        this.elements.charCount.textContent = `${count} / ${maxLength}`;
        this.elements.charCount.style.color = count > maxLength ? 'var(--danger-color)' : 'var(--text-muted)';

        // 禁用/启用分析按钮
        this.elements.analyzeTextBtn.disabled = count === 0 || count > maxLength;
    }

    /**
     * 更新预览
     */
    updatePreview() {
        const text = this.elements.textInput.value.trim();

        if (!text) {
            this.elements.previewContent.innerHTML = '<div class="preview-placeholder">输入文案后查看智能切片预览</div>';
            return;
        }

        // 智能切片
        const chunks = this.api.splitTextIntoChunks(text);

        let previewHtml = '<div class="chunks-preview">';
        chunks.forEach((chunk, index) => {
            previewHtml += `
                <div class="chunk-item">
                    <div class="chunk-header">片段 ${index + 1} (${chunk.length} 字符)</div>
                    <div class="chunk-content">${this.escapeHtml(chunk)}</div>
                </div>
            `;
        });
        previewHtml += '</div>';

        this.elements.previewContent.innerHTML = previewHtml;
    }

    /**
     * 统一分析调度器
     */
    async handleAnalysis(analysisType, options = {}) {
        const analysisConfig = {
            text: {
                intelligent: () => this.analyzeText(),
                chat: () => this.analyzeTextInChatMode()
            },
            image: {
                intelligent: () => this.analyzeImage(),
                chat: () => this.analyzeImageInChatMode()
            },
            imageText: {
                intelligent: () => this.analyzeImageText(),
                chat: () => this.analyzeImageTextInChatMode()
            }
        };

        const handler = analysisConfig[analysisType]?.[this.currentMode];
        if (handler) {
            await handler(options);
        } else {
            throw new Error(`不支持的分析类型: ${analysisType} (模式: ${this.currentMode})`);
        }
    }

    /**
     * 处理文案分析按钮点击
     */
    async handleAnalyzeText() {
        try {
            await this.handleAnalysis('text');
        } catch (error) {
            console.error('文案分析失败:', error);
            this.showToast('分析失败，请重试', 'error');
        }
    }

    /**
     * 处理图片分析按钮点击
     */
    async handleAnalyzeImage() {
        try {
            await this.handleAnalysis('image');
        } catch (error) {
            console.error('图片分析失败:', error);
            this.showToast('分析失败，请重试', 'error');
        }
    }

    /**
     * 处理图文分析按钮点击
     */
    async handleAnalyzeImageText() {
        try {
            await this.handleAnalysis('imageText');
        } catch (error) {
            console.error('图文分析失败:', error);
            this.showToast('分析失败，请重试', 'error');
        }
    }

    /**
     * 对话模式文案分析（通过分析按钮触发）
     */
    async analyzeTextInChatMode() {
        const text = this.elements.textInput.value.trim();

        if (!text) {
            this.showToast('请输入要分析的文案', 'warning');
            return;
        }

        if (!this.api.isApiKeyAvailable()) {
            this.promptForApiKey();
            return;
        }

        this.setProcessing(true);

        try {
            // 添加用户消息
            this.addMessage('user', `请分析以下文案：\n\n${text}`);

            // 直接调用对话API，使用流式输出
            let streamingMessageDiv = null;
            let streamingContent = '';
            let reasoningContent = '';

            const message = `请分析以下文案：\n\n${text}`;
            const response = await this.api.chat(message, this.conversationHistory, 'chat', this.currentImage, (chunk, type) => {
                // 流式输出回调
                if (type === 'reasoning') {
                    // 思考过程
                    reasoningContent += chunk;
                } else {
                    // 主要内容
                    streamingContent += chunk;
                }

                if (!streamingMessageDiv) {
                    // 创建流式消息容器
                    streamingMessageDiv = this.createStreamingMessage();
                }

                // 更新流式消息内容
                this.updateStreamingMessage(streamingMessageDiv, streamingContent, reasoningContent);
            });

            // 流式输出完成后，移除streaming类
            if (streamingMessageDiv) {
                streamingMessageDiv.classList.remove('streaming');
            } else {
                // 如果没有流式输出（可能是错误情况），直接添加消息
                this.addMessage('assistant', response);
            }

            // 更新对话历史
            this.conversationHistory.push(
                { role: 'user', content: message },
                { role: 'assistant', content: response }
            );

            // 保存对话历史
            this.saveConversationHistory();

        } catch (error) {
            console.error('对话分析失败:', error);
            this.addMessage('assistant', `❌ 分析失败: ${error.message}`);
        } finally {
            this.setProcessing(false);
        }
    }

    /**
     * 对话模式图片分析（通过分析按钮触发）
     */
    async analyzeImageInChatMode() {
        if (!this.currentImage) {
            this.showToast('请先上传图片', 'warning');
            return;
        }

        if (!this.api.isApiKeyAvailable()) {
            this.promptForApiKey();
            return;
        }

        this.setProcessing(true);

        try {
            // 添加用户消息
            this.addMessage('user', '请分析这张图片的内容和合规性');

            // 直接调用对话API，使用流式输出
            let streamingMessageDiv = null;
            let streamingContent = '';
            let reasoningContent = '';

            const message = '请分析这张图片的内容和合规性';
            const response = await this.api.chat(message, this.conversationHistory, 'chat', this.currentImage, (chunk, type) => {
                // 流式输出回调
                if (type === 'reasoning') {
                    // 思考过程
                    reasoningContent += chunk;
                } else {
                    // 主要内容
                    streamingContent += chunk;
                }

                if (!streamingMessageDiv) {
                    // 创建流式消息容器
                    streamingMessageDiv = this.createStreamingMessage();
                }

                // 更新流式消息内容
                this.updateStreamingMessage(streamingMessageDiv, streamingContent, reasoningContent);
            });

            // 流式输出完成后，移除streaming类
            if (streamingMessageDiv) {
                streamingMessageDiv.classList.remove('streaming');
            } else {
                // 如果没有流式输出（可能是错误情况），直接添加消息
                this.addMessage('assistant', response);
            }

            // 更新对话历史
            this.conversationHistory.push(
                { role: 'user', content: message },
                { role: 'assistant', content: response }
            );

            // 保存对话历史
            this.saveConversationHistory();

        } catch (error) {
            console.error('对话图片分析失败:', error);
            this.addMessage('assistant', `❌ 图片分析失败: ${error.message}`);
        } finally {
            this.setProcessing(false);
        }
    }

    /**
     * 对话模式图文分析（通过分析按钮触发）
     */
    async analyzeImageTextInChatMode() {
        if (!this.currentImage) {
            this.showToast('请先上传图片', 'warning');
            return;
        }

        const textContent = this.elements.imageTextInput.value.trim();
        if (!textContent) {
            this.showToast('请输入配套文案', 'warning');
            return;
        }

        if (!this.api.isApiKeyAvailable()) {
            this.promptForApiKey();
            return;
        }

        this.setProcessing(true);

        try {
            // 添加用户消息
            const userMessage = `请分析这张图片和配套文案的合规性：\n\n${textContent}`;
            this.addMessage('user', userMessage);

            // 直接调用对话API，使用流式输出
            let streamingMessageDiv = null;
            let streamingContent = '';
            let reasoningContent = '';

            const response = await this.api.chat(userMessage, this.conversationHistory, 'chat', this.currentImage, (chunk, type) => {
                // 流式输出回调
                if (type === 'reasoning') {
                    // 思考过程
                    reasoningContent += chunk;
                } else {
                    // 主要内容
                    streamingContent += chunk;
                }

                if (!streamingMessageDiv) {
                    // 创建流式消息容器
                    streamingMessageDiv = this.createStreamingMessage();
                }

                // 更新流式消息内容
                this.updateStreamingMessage(streamingMessageDiv, streamingContent, reasoningContent);
            });

            // 流式输出完成后，移除streaming类
            if (streamingMessageDiv) {
                streamingMessageDiv.classList.remove('streaming');
            } else {
                // 如果没有流式输出（可能是错误情况），直接添加消息
                this.addMessage('assistant', response);
            }

            // 更新对话历史
            this.conversationHistory.push(
                { role: 'user', content: userMessage },
                { role: 'assistant', content: response }
            );

            // 保存对话历史
            this.saveConversationHistory();

        } catch (error) {
            console.error('对话图文分析失败:', error);
            this.addMessage('assistant', `❌ 图文分析失败: ${error.message}`);
        } finally {
            this.setProcessing(false);
        }
    }

    /**
     * 分析文案
     */
    async analyzeText() {
        const text = this.elements.textInput.value.trim();

        if (!text) {
            this.showToast('请输入要分析的文案', 'warning');
            return;
        }

        if (!this.api.isApiKeyAvailable()) {
            this.promptForApiKey();
            return;
        }

        // 启动倒计时（预估15秒）
        this.setProcessing(true, this.elements.analyzeBtn, 15);

        try {
            // 智能体模式：显示详细的分析进度
            await this.performIntelligentAnalysis(text);

        } catch (error) {
            console.error('文案分析失败:', error);
            this.addMessage('assistant', `❌ 分析失败: ${error.message}`);
            this.showToast('分析失败，请重试', 'error');
        } finally {
            this.setProcessing(false);
        }
    }

    /**
     * 智能体模式分析（带进度显示）
     */
    async performIntelligentAnalysis(text) {
        console.log('🔍 开始智能体模式分析，显示进度卡片...');

        // 检查进度管理器是否存在
        if (!this.progressManager) {
            console.error('❌ 进度管理器未初始化');
            this.progressManager = new ProgressManager(this.elements.chatMessages);
        }

        // 显示分析进度动画
        const progressId = this.progressManager.show('text');
        console.log('✅ 进度卡片已创建，ID:', progressId);

        try {
            // 本地关键词检测
            console.log('🔍 步骤1: 本地关键词检测');
            this.progressManager.updateStep(progressId, 0, 'active');
            await this.delay(1000);
            const localIssues = this.api.detectLocalKeywords(text);
            this.progressManager.updateStep(progressId, 0, 'completed');

            // AI分析
            console.log('🤖 步骤2: AI智能分析');
            this.progressManager.updateStep(progressId, 1, 'active');
            await this.delay(500);
            const result = await this.api.auditText(text, this.currentMode);
            this.progressManager.updateStep(progressId, 1, 'completed');

            // 结果处理
            console.log('📋 步骤3: 结果整理生成');
            this.progressManager.updateStep(progressId, 2, 'active');
            await this.delay(800);

            // 合并结果
            if (localIssues.length > 0 && result.issues) {
                result.issues = [...result.issues, ...localIssues];
            }

            this.progressManager.updateStep(progressId, 2, 'completed');
            await this.delay(500);

            // 显示结果
            console.log('✅ 分析完成，显示结果');
            this.displayAuditResult(result, 'text');

        } finally {
            // 移除进度动画
            console.log('🗑️ 移除进度卡片');
            this.progressManager.stopTimer(progressId);
            this.progressManager.remove(progressId);
        }
    }



    /**
     * 处理文档上传（重构版）
     */
    async handleDocumentUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        try {
            // 验证文件
            FileValidator.validateTextFile(file, this.config);

            // 读取文本内容
            const text = await TextProcessor.readTextFile(file);

            // 更新界面
            this.elements.textInput.value = text;
            this.updateCharCount();
            this.updatePreview();
            this.showToast('文档上传成功', 'success');

        } catch (error) {
            console.error('文档上传失败:', error);
            this.showToast(error.message || '文档读取失败', 'error');
        }
    }

    // 旧的 readTextFile 函数已被 TextProcessor.readTextFile 替代

    /**
     * 清空文案
     */
    clearText() {
        this.elements.textInput.value = '';
        this.updateCharCount();
        this.updatePreview();
        this.showToast('文案已清空', 'success');
    }

    /**
     * 处理拖拽
     */
    handleDragOver(event) {
        event.preventDefault();
        this.elements.uploadArea.classList.add('dragover');
    }

    /**
     * 处理拖拽放置
     */
    handleDrop(event) {
        event.preventDefault();
        this.elements.uploadArea.classList.remove('dragover');

        const files = event.dataTransfer.files;
        if (files.length > 0) {
            this.processImageFile(files[0]);
        }
    }

    /**
     * 处理图片上传
     */
    handleImageUpload(event) {
        const file = event.target.files[0];
        if (file) {
            this.processImageFile(file);
        }
    }

    /**
     * 处理图片文件（重构版）
     */
    async processImageFile(file) {
        try {
            // 验证文件
            FileValidator.validateImageFile(file, this.config);

            // 生成预览
            const imageUrl = await ImageProcessor.generatePreview(file);

            // 显示预览
            this.showImagePreview(file, imageUrl);

            this.showToast('图片上传成功', 'success');

        } catch (error) {
            console.error('图片处理失败:', error);
            this.showToast(error.message || '图片处理失败', 'error');
        }
    }

    // 旧的 readImageFile 函数已被 ImageProcessor.generatePreview 替代

    /**
     * 显示图片预览
     */
    showImagePreview(file, imageUrl) {
        this.currentImage = {
            file: file,
            url: imageUrl,
            name: file.name,
            size: ImageProcessor.formatFileSize(file.size)
        };

        this.elements.previewImage.src = imageUrl;
        this.elements.imageName.textContent = file.name;
        this.elements.imageSize.textContent = ImageProcessor.formatFileSize(file.size);

        this.elements.imagePreview.style.display = 'block';
        this.elements.imageTextGroup.style.display = 'block';
        this.elements.imageButtons.style.display = 'flex';

        // 检查是否有配套文案
        this.updateImageAnalysisButtons();
    }

    /**
     * 移除图片
     */
    removeImage() {
        this.currentImage = null;
        this.elements.imagePreview.style.display = 'none';
        this.elements.imageTextGroup.style.display = 'none';
        this.elements.imageButtons.style.display = 'none';
        this.elements.fileInput.value = '';
        this.showToast('图片已移除', 'success');
    }

    /**
     * 更新图片分析按钮状态
     */
    updateImageAnalysisButtons() {
        const hasText = this.elements.imageTextInput.value.trim().length > 0;
        this.elements.analyzeImageTextBtn.style.display = hasText ? 'flex' : 'none';
    }

    /**
     * 分析图片
     */
    async analyzeImage() {
        if (!this.currentImage) {
            this.showToast('请先上传图片', 'warning');
            return;
        }

        if (!this.api.isApiKeyAvailable()) {
            this.promptForApiKey();
            return;
        }

        // 启动倒计时（预估20秒，图片分析较慢）
        this.setProcessing(true, this.elements.imageAnalyzeBtn, 20);

        try {
            // 智能体模式：显示详细的分析进度
            await this.performIntelligentImageAnalysis();

        } catch (error) {
            console.error('图片分析失败:', error);
            this.addMessage('assistant', `❌ 图片分析失败: ${error.message}`);
            this.showToast('图片分析失败，请重试', 'error');
        } finally {
            this.setProcessing(false);
        }
    }

    /**
     * 智能体模式图片分析（带进度显示）
     */
    async performIntelligentImageAnalysis() {
        console.log('🖼️ 开始智能体模式图片分析，显示进度卡片...');

        // 检查进度管理器是否存在
        if (!this.progressManager) {
            console.error('❌ 进度管理器未初始化');
            this.progressManager = new ProgressManager(this.elements.chatMessages);
        }

        // 显示图片分析进度动画
        const progressId = this.progressManager.show('image');
        console.log('✅ 图片分析进度卡片已创建，ID:', progressId);

        try {
            // 图片内容识别
            console.log('🔍 步骤1: 图片内容识别');
            this.progressManager.updateStep(progressId, 0, 'active');
            await this.delay(1500);
            this.progressManager.updateStep(progressId, 0, 'completed');

            // AI智能分析
            console.log('🤖 步骤2: AI智能分析');
            this.progressManager.updateStep(progressId, 1, 'active');
            await this.delay(800);
            const result = await this.api.auditImage(this.currentImage.url, '', this.currentMode);
            this.progressManager.updateStep(progressId, 1, 'completed');

            // 结果生成
            console.log('📋 步骤3: 结果生成报告');
            this.progressManager.updateStep(progressId, 2, 'active');
            await this.delay(1000);
            this.progressManager.updateStep(progressId, 2, 'completed');
            await this.delay(500);

            // 显示结果
            console.log('✅ 图片分析完成，显示结果');
            this.displayAuditResult(result, 'image');

        } finally {
            // 移除进度动画
            console.log('🗑️ 移除图片分析进度卡片');
            this.progressManager.stopTimer(progressId);
            this.progressManager.remove(progressId);
        }
    }



    /**
     * 分析图文内容
     */
    async analyzeImageText() {
        if (!this.currentImage) {
            this.showToast('请先上传图片', 'warning');
            return;
        }

        const textContent = this.elements.imageTextInput.value.trim();
        if (!textContent) {
            this.showToast('请输入配套文案', 'warning');
            return;
        }

        if (!this.api.isApiKeyAvailable()) {
            this.promptForApiKey();
            return;
        }

        this.setProcessing(true);

        try {
            if (this.currentMode === 'intelligent') {
                // 智能体模式：显示分析提示
                this.addMessage('assistant', '🔍 开始进行图文综合分析，请稍候...');
            }

            // 先分析图片
            const imageResult = await this.api.auditImage(this.currentImage.url, textContent, this.currentMode);

            // 再进行综合分析
            const comprehensiveResult = await this.api.auditComprehensive(textContent, imageResult);

            this.displayAuditResult(comprehensiveResult, 'comprehensive');

        } catch (error) {
            console.error('图文分析失败:', error);
            this.addMessage('assistant', `❌ 图文分析失败: ${error.message}`);
            this.showToast('图文分析失败，请重试', 'error');
        } finally {
            this.setProcessing(false);
        }
    }

    /**
     * 显示审核结果
     */
    displayAuditResult(result, type) {
        if (this.currentMode === 'intelligent') {
            // 智能体模式：显示卡片式可视化结果
            this.displayIntelligentAnalysisResult(result);
        } else {
            // 对话模式：显示传统格式结果
            this.displayTraditionalAuditResult(result, type);
        }
    }

    /**
     * 智能体模式可视化分析结果显示
     */
    displayIntelligentAnalysisResult(result) {
        try {
            // 解析和标准化结果数据
            const parsedResult = this.parseAnalysisResult(result);

            // 1. 首先显示智能切片可视化组件
            if (parsedResult.sentence_analysis && parsedResult.sentence_analysis.length > 0) {
                this.displayIntelligentSegments(parsedResult.sentence_analysis);
            }

            // 2. 然后显示卡片式分析结果
            this.displayAnalysisCards(parsedResult);

        } catch (error) {
            console.error('显示智能分析结果失败:', error);
            this.addMessage('assistant', '❌ **分析结果显示失败**\n\n数据处理异常，请重试或联系技术支持。');
        }
    }

    /**
     * 显示智能切片可视化组件
     */
    displayIntelligentSegments(segments) {
        const messagesContainer = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant intelligent-segments';

        const now = new Date();
        const time = `${now.getMonth() + 1}月${now.getDate()}日${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;

        // 统计风险等级
        const riskStats = this.calculateRiskStats(segments);

        // 生成统计HTML
        const statsHTML = `
            <div class="risk-statistics">
                <div class="stat-item">
                    <div class="stat-emoji">🔴</div>
                    <div class="stat-label">严重违规</div>
                    <div class="stat-count">${riskStats.urgent}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-emoji">🟠</div>
                    <div class="stat-label">需要警告</div>
                    <div class="stat-count">${riskStats.warning}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-emoji">🟣</div>
                    <div class="stat-label">需要注意</div>
                    <div class="stat-count">${riskStats.notice}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-emoji">🟢</div>
                    <div class="stat-label">内容安全</div>
                    <div class="stat-count">${riskStats.safe}</div>
                </div>
            </div>
        `;

        // 生成切片HTML
        const segmentsHTML = segments.map(segment => {
            const riskClass = segment.risk_level || 'safe';
            const riskEmoji = this.getRiskEmoji(riskClass);

            return `
                <div class="intelligent-segment ${riskClass}">
                    <div class="segment-risk ${riskClass}">
                        ${riskEmoji} ${this.getRiskText(riskClass)}
                    </div>
                    <div class="segment-content">${segment.sentence}</div>
                    ${segment.issues && segment.issues.length > 0 ? `
                        <div class="segment-reason">
                            问题：${segment.issues.join('、')}
                        </div>
                    ` : ''}
                    ${segment.highlights && segment.highlights.length > 0 ? `
                        <div class="segment-suggestion">
                            💡 关键词：${segment.highlights.join('、')}
                        </div>
                    ` : ''}
                </div>
            `;
        }).join('');

        messageDiv.innerHTML = `
            <div class="message-avatar">🤖</div>
            <div class="message-content">
                <div class="message-text">
                    <div class="intelligent-analysis-header">
                        <h4>📋 智能切片分析结果</h4>
                        <p>基于AI大模型的语句级风险评估</p>
                    </div>
                    ${statsHTML}
                    <div class="intelligent-segments-container">
                        ${segmentsHTML}
                    </div>
                </div>
                <div class="message-time">${time}</div>
            </div>
        `;

        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        // 添加动画效果
        messageDiv.classList.add('fade-in');

        return messageDiv;
    }

    /**
     * 显示卡片式分析结果
     */
    displayAnalysisCards(parsedResult) {
        const messagesContainer = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant analysis-cards';

        const now = new Date();
        const time = `${now.getMonth() + 1}月${now.getDate()}日${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;

        // 统计信息
        const stats = this.calculateAnalysisStats(parsedResult);

        // 整体风险评估
        const riskEmoji = this.getRiskEmoji(parsedResult.overall_risk);
        const riskText = this.getRiskText(parsedResult.overall_risk);
        const riskClass = parsedResult.overall_risk || 'safe';

        // 生成卡片HTML
        const cardsHTML = this.generateAnalysisCardsHTML(parsedResult, stats, riskEmoji, riskText, riskClass);

        messageDiv.innerHTML = `
            <div class="message-avatar">🤖</div>
            <div class="message-content">
                <div class="message-text">
                    <div class="analysis-cards-header">
                        <h4>📋 智能体分析结果</h4>
                        <p>基于AI大模型的深度内容审核</p>
                    </div>
                    ${cardsHTML}
                </div>
                <div class="message-time">${time}</div>
            </div>
        `;

        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        // 添加动画效果
        messageDiv.classList.add('fade-in');

        return messageDiv;
    }

    /**
     * 传统审核结果显示（对话模式）
     */
    displayTraditionalAuditResult(result, type) {
        // 对话模式：直接显示原始响应，不包装成卡片
        if (result.raw_response) {
            // 直接显示AI的原始回复，保持流式输出的自然感
            this.addMessage('assistant', result.raw_response);
        } else {
            // 如果没有原始响应，生成简化的文本格式
            let textResult = `📋 **审核结果**\n\n`;

            const riskConfig = this.config.riskLevels[result.overall_risk] || this.config.riskLevels.notice;
            textResult += `${riskConfig.icon} **风险等级**: ${riskConfig.name}\n`;
            textResult += `📊 **综合评分**: ${result.overall_score}/100\n\n`;

            if (result.summary) {
                textResult += `**审核摘要**: ${result.summary}\n\n`;
            }

            if (result.issues && result.issues.length > 0) {
                textResult += `⚠️ **发现问题** (${result.issues.length}个):\n`;
                result.issues.forEach((issue, index) => {
                    const issueRisk = this.config.riskLevels[issue.level] || this.config.riskLevels.notice;
                    textResult += `${index + 1}. ${issueRisk.icon} ${issue.text || issue.description}\n`;
                    if (issue.reason) {
                        textResult += `   原因: ${issue.reason}\n`;
                    }
                    if (issue.suggestion) {
                        textResult += `   建议: ${issue.suggestion}\n`;
                    }
                    textResult += '\n';
                });
            }

            if (result.suggestions && result.suggestions.length > 0) {
                textResult += `💡 **修改建议**:\n`;
                result.suggestions.forEach((suggestion, index) => {
                    if (suggestion && suggestion.trim()) {
                        textResult += `${index + 1}. ${suggestion}\n`;
                    }
                });
            }

            this.addMessage('assistant', textResult);
        }
    }

    /**
     * 解析分析结果
     */
    parseAnalysisResult(result) {
        console.log('🔍 开始解析分析结果:', result);

        // 如果已经是解析后的格式，直接返回
        if (result && typeof result === 'object' && !result.raw_response) {
            console.log('✅ 直接使用已解析的结果');
            return this.normalizeAnalysisResult(result);
        }

        // 如果有raw_response，尝试解析
        if (result && result.raw_response) {
            try {
                console.log('🔄 尝试解析raw_response...');
                // 尝试从raw_response中提取JSON
                const jsonMatch = result.raw_response.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    const parsed = JSON.parse(jsonMatch[0]);
                    console.log('✅ 成功解析JSON:', parsed);
                    return this.normalizeAnalysisResult({ ...result, ...parsed });
                }
            } catch (error) {
                console.warn('❌ 解析raw_response失败:', error);
            }
        }

        console.log('🔄 使用默认格式处理...');
        return this.normalizeAnalysisResult(result);
    }

    /**
     * 标准化分析结果格式
     */
    normalizeAnalysisResult(result) {
        // 返回默认格式，兼容多种结构
        const parsedResult = {
            overall_risk: result?.overall_risk || 'safe',
            overall_score: result?.overall_score || 80,
            summary: result?.summary || '分析完成',
            typo_errors: result?.typo_errors || [],
            keyword_highlights: result?.keyword_highlights || [],
            violations: result?.violations || [],
            false_claims: result?.false_claims || [],
            issues: result?.issues || [],
            suggestions: result?.suggestions || []
        };

        // 处理图片分析特有字段
        if (result?.image_description) {
            parsedResult.image_description = result.image_description;
            parsedResult.extracted_text = result.extracted_text;
            parsedResult.structured_info = result.structured_info;
            parsedResult.consistency_check = result.consistency_check;
            parsedResult.risk_assessment = result.risk_assessment;

            // 为图片分析创建虚拟的sentence_analysis
            parsedResult.sentence_analysis = [
                {
                    sentence: result.image_description || '图片内容描述',
                    risk_level: result.overall_risk || 'safe',
                    issues: result.issues ? result.issues.map(issue => issue.description || issue.reason || issue.text || '未知问题') : [],
                    highlights: [],
                    position: '图片内容'
                }
            ];

            // 如果有提取的文字，添加到sentence_analysis
            if (result.extracted_text && result.extracted_text !== '无') {
                parsedResult.sentence_analysis.push({
                    sentence: result.extracted_text,
                    risk_level: result.overall_risk || 'safe',
                    issues: [],
                    highlights: [],
                    position: '图片文字'
                });
            }
        }
        // 处理segments结构（原代码库格式）
        else if (result?.segments) {
            parsedResult.segments = result.segments;
            // 为了兼容，将segments转换为sentence_analysis
            parsedResult.sentence_analysis = result.segments.map((segment, index) => ({
                sentence: segment.text,
                risk_level: segment.risk_level,
                issues: segment.reason ? [segment.reason] : [],
                highlights: segment.annotations ?
                    segment.annotations.filter(a => a.type === 'keyword').map(a => a.original) : [],
                position: `段落${index + 1}`
            }));
        }
        // 处理sentence_analysis结构（当前格式）
        else if (result?.sentence_analysis) {
            parsedResult.sentence_analysis = result.sentence_analysis;
        }
        // 如果没有sentence_analysis，创建一个基础的
        else {
            parsedResult.sentence_analysis = [
                {
                    sentence: parsedResult.summary,
                    risk_level: parsedResult.overall_risk,
                    issues: parsedResult.issues.map(issue => issue.description || issue.reason || issue.text || '未知问题'),
                    highlights: [],
                    position: '整体分析'
                }
            ];
        }

        console.log('✅ 标准化结果完成:', parsedResult);
        return parsedResult;
    }

    /**
     * 计算风险统计
     */
    calculateRiskStats(segments) {
        const stats = { urgent: 0, warning: 0, notice: 0, safe: 0 };

        segments.forEach(segment => {
            const risk = segment.risk_level || 'safe';
            if (stats.hasOwnProperty(risk)) {
                stats[risk]++;
            }
        });

        return stats;
    }

    /**
     * 计算分析统计
     */
    calculateAnalysisStats(result) {
        return {
            typos: result.typo_errors?.length || 0,
            keywords: result.keyword_highlights?.length || 0,
            violations: result.violations?.length || 0,
            falseClaims: result.false_claims?.length || 0,
            totalIssues: (result.violations?.length || 0) + (result.false_claims?.length || 0)
        };
    }

    /**
     * 获取风险表情符号
     */
    getRiskEmoji(riskLevel) {
        const emojiMap = {
            urgent: '🔴',
            warning: '🟠',
            notice: '🟣',
            safe: '🟢'
        };
        return emojiMap[riskLevel] || '🟢';
    }

    /**
     * 获取风险文本
     */
    getRiskText(riskLevel) {
        const textMap = {
            urgent: '严重违规',
            warning: '需要警告',
            notice: '需要注意',
            safe: '内容安全'
        };
        return textMap[riskLevel] || '内容安全';
    }

    /**
     * 生成分析卡片HTML
     */
    generateAnalysisCardsHTML(parsedResult, stats, riskEmoji, riskText, riskClass) {
        let cardsHTML = '';

        // 1. 整体评估卡片
        cardsHTML += `
            <div class="analysis-card overall-assessment ${riskClass}">
                <div class="card-header">
                    <div class="card-icon">${riskEmoji}</div>
                    <div class="card-title">整体风险评估</div>
                </div>
                <div class="card-content">
                    <div class="risk-level">${riskText}</div>
                    ${parsedResult.summary ? `<div class="risk-summary">${parsedResult.summary}</div>` : ''}
                </div>
            </div>
        `;

        // 2. 图片分析专用卡片（如果有图片描述）
        if (parsedResult.image_description) {
            cardsHTML += `
                <div class="analysis-card image-analysis-card">
                    <div class="card-header">
                        <div class="card-icon">🖼️</div>
                        <div class="card-title">图片内容分析</div>
                    </div>
                    <div class="card-content">
                        <div class="image-description">
                            <h5>📋 图片描述</h5>
                            <p>${parsedResult.image_description}</p>
                        </div>
                        ${parsedResult.extracted_text && parsedResult.extracted_text !== '无' ? `
                            <div class="extracted-text">
                                <h5>📝 提取的文字</h5>
                                <p>${parsedResult.extracted_text}</p>
                            </div>
                        ` : ''}
                        ${parsedResult.structured_info && parsedResult.structured_info !== '无' ? `
                            <div class="structured-info">
                                <h5>📊 结构化信息</h5>
                                <p>${parsedResult.structured_info}</p>
                            </div>
                        ` : ''}
                        ${parsedResult.consistency_check ? `
                            <div class="consistency-check">
                                <h5>🔍 图文一致性</h5>
                                <p><strong>结果：</strong>${parsedResult.consistency_check.result || '未检测'}</p>
                                ${parsedResult.consistency_check.details ? `<p><strong>详情：</strong>${parsedResult.consistency_check.details}</p>` : ''}
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        // 2. 统计卡片
        cardsHTML += `
            <div class="analysis-card statistics-card">
                <div class="card-header">
                    <div class="card-icon">📊</div>
                    <div class="card-title">检测统计</div>
                </div>
                <div class="card-content">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value">${stats.typos}</div>
                            <div class="stat-description">错别字</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${stats.keywords}</div>
                            <div class="stat-description">关键词</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${stats.violations}</div>
                            <div class="stat-description">违规内容</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${stats.falseClaims}</div>
                            <div class="stat-description">虚假夸大</div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 3. 问题列表卡片（如果有问题）
        if (stats.totalIssues > 0) {
            const allIssues = [...(parsedResult.violations || []), ...(parsedResult.false_claims || [])];

            cardsHTML += `
                <div class="analysis-card issues-card">
                    <div class="card-header">
                        <div class="card-icon">⚠️</div>
                        <div class="card-title">发现问题 (${stats.totalIssues}个)</div>
                    </div>
                    <div class="card-content">
                        ${allIssues.map(issue => `
                            <div class="issue-item">
                                <div class="issue-header">
                                    <span class="issue-risk">${this.getRiskEmoji(issue.risk_level || 'warning')}</span>
                                    <span class="issue-text">${issue.content}</span>
                                </div>
                                <div class="issue-description">${issue.reason}</div>
                                ${issue.suggestion ? `<div class="issue-suggestion">💡 ${issue.suggestion}</div>` : ''}
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        // 4. 关键词卡片（如果有关键词）
        if (stats.keywords > 0) {
            cardsHTML += `
                <div class="analysis-card keywords-card">
                    <div class="card-header">
                        <div class="card-icon">🔍</div>
                        <div class="card-title">关键词标注 (${stats.keywords}个)</div>
                    </div>
                    <div class="card-content">
                        ${parsedResult.keyword_highlights.map(keyword => `
                            <span class="keyword-item">
                                ${keyword.keyword || keyword.text || '未知关键词'}
                                ${keyword.type ? `<span class="keyword-type">${keyword.type}</span>` : ''}
                            </span>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        return cardsHTML;
    }

    // 旧的 formatFileSize 函数已被 ImageProcessor.formatFileSize 替代

    /**
     * HTML转义
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 设置处理状态
     */
    setProcessing(isProcessing, buttonElement = null, countdownSeconds = 0) {
        this.isProcessing = isProcessing;

        // 禁用/启用相关按钮
        const buttons = [
            this.elements.analyzeTextBtn,
            this.elements.analyzeImageBtn,
            this.elements.analyzeImageTextBtn,
            this.elements.sendBtn
        ];

        buttons.forEach(btn => {
            if (btn) btn.disabled = isProcessing;
        });

        // 更新发送按钮文本
        if (this.elements.sendBtn) {
            this.elements.sendBtn.textContent = isProcessing ? '处理中...' : '发送';
        }

        // 如果指定了按钮和倒计时，启动倒计时
        if (buttonElement && countdownSeconds > 0 && isProcessing) {
            this.startCountdown(buttonElement, countdownSeconds);
        }
    }

    /**
     * 启动倒计时
     */
    startCountdown(buttonElement, seconds) {
        const originalText = buttonElement.textContent;
        let remainingSeconds = seconds;

        const updateButton = () => {
            if (remainingSeconds > 0 && this.isProcessing && !this.isDestroyed) {
                buttonElement.textContent = `分析中... ${remainingSeconds}s`;
                remainingSeconds--;
                // 使用内存管理器注册定时器
                if (this.memoryManager) {
                    this.memoryManager.registerTimer(setTimeout(updateButton, 1000));
                } else {
                    setTimeout(updateButton, 1000);
                }
            } else if (!this.isProcessing) {
                buttonElement.textContent = originalText;
            }
        };

        updateButton();
    }

    /**
     * 隐藏加载动画
     */
    hideLoadingOverlay() {
        setTimeout(() => {
            this.elements.loadingOverlay.classList.add('hidden');
        }, 1000);
    }

    /**
     * 检查API密钥
     */
    checkApiKey() {
        // 更新连接状态显示
        this.updateConnectionStatus();

        if (!this.api.isApiKeyAvailable()) {
            setTimeout(() => {
                this.addMessage('assistant', `
                    🔑 <strong>API密钥配置指南</strong><br><br>

                    <div style="background: #f0f9ff; padding: 12px; border-radius: 6px; border-left: 4px solid #3b82f6; margin: 10px 0;">
                        <strong>💡 推荐方式：手动配置</strong><br>
                        在聊天框中输入：<code>设置API密钥 your-modelscope-key</code>
                    </div>

                    <strong>📋 获取API密钥步骤：</strong><br>
                    1. 访问 <a href="https://dashscope.console.aliyun.com/" target="_blank">魔搭创空间控制台</a><br>
                    2. 登录您的账号<br>
                    3. 获取API密钥<br>
                    4. 在下方输入框中输入：<code>设置API密钥 your-key</code><br><br>

                    <strong>🔍 其他配置方式：</strong><br>
                    • 输入 <code>环境变量检测</code> 查看详细检测报告<br>
                    • 输入 <code>API密钥配置帮助</code> 获取更多帮助<br><br>

                    <div style="background: #ecfdf5; padding: 10px; border-radius: 6px; border-left: 4px solid #059669;">
                        <strong>✨ 配置完成后即可使用所有AI功能</strong>：智能分析、图片审核、法规查询等
                    </div>
                `);
            }, 2000);
        }
    }

    /**
     * 更新连接状态显示
     */
    updateConnectionStatus() {
        const statusElement = document.getElementById('chat-status');
        const statusDot = statusElement?.querySelector('.status-dot');

        if (statusElement && statusDot) {
            if (this.api.isApiKeyAvailable()) {
                // API密钥已配置
                statusDot.className = 'status-dot online';
                statusElement.innerHTML = '<span class="status-dot online"></span>AI已连接';
                statusElement.style.color = 'var(--success-color)';
            } else {
                // API密钥未配置
                statusDot.className = 'status-dot offline';
                statusElement.innerHTML = '<span class="status-dot offline"></span>AI未连接';
                statusElement.style.color = 'var(--danger-color)';
            }
        }
    }

    /**
     * 检查环境变量配置情况
     */
    checkEnvironmentVariables() {
        const checks = {
            localStorage: !!localStorage.getItem(this.api.config.storage.prefix + this.api.config.storage.keys.apiKey),
            processEnv: typeof process !== 'undefined' && !!process.env && !!process.env.MODELSCOPE_API_KEY,
            windowGlobal: typeof window !== 'undefined' && !!window.MODELSCOPE_API_KEY,
            urlParams: typeof window !== 'undefined' && window.location && !!(new URLSearchParams(window.location.search).get('modelscope_api_key'))
        };

        const hasEnvSupport = checks.processEnv || checks.windowGlobal;

        let details = '';
        details += `• localStorage: ${checks.localStorage ? '✅ 已设置' : '❌ 未设置'}<br>`;
        details += `• process.env: ${checks.processEnv ? '✅ 支持' : '❌ 不支持'}<br>`;
        details += `• window全局变量: ${checks.windowGlobal ? '✅ 已注入' : '❌ 未注入'}<br>`;
        details += `• URL参数: ${checks.urlParams ? '✅ 存在' : '❌ 不存在'}<br>`;

        // 添加详细的环境变量来源信息
        if (checks.windowGlobal) {
            details += `<br><strong>🔍 检测到的环境变量：</strong><br>`;
            if (window.MODELSCOPE_API_KEY) {
                details += `• window.MODELSCOPE_API_KEY: ${window.MODELSCOPE_API_KEY.substring(0, 8)}...<br>`;
            }
        }

        return {
            hasEnvSupport,
            details,
            checks
        };
    }

    /**
     * 提示输入API密钥
     */
    promptForApiKey() {
        this.addMessage('assistant', `
            🔑 <strong>需要配置API密钥</strong><br><br>
            请在聊天框中输入：<code>设置API密钥 your-api-key</code><br>
            或者输入：<code>API密钥配置帮助</code> 获取详细说明
        `);
    }

    /**
     * 发送消息
     */
    async sendMessage() {
        const message = this.elements.chatInput.value.trim();
        if (!message || this.isProcessing) return;

        // 清空输入框
        this.elements.chatInput.value = '';

        // 添加用户消息到界面
        this.addMessage('user', message);

        // 检查特殊命令
        if (await this.handleSpecialCommands(message)) {
            return;
        }

        if (!this.api.isApiKeyAvailable()) {
            this.promptForApiKey();
            return;
        }

        this.setProcessing(true);

        try {
            console.log('📝 发送消息:', message);
            console.log('📚 当前对话历史长度:', this.conversationHistory.length);
            console.log('🎯 当前模式:', this.currentMode);

            let response;
            let streamingMessageDiv = null;
            let streamingContent = '';
            let reasoningContent = '';

            // 统一使用流式输出，根据模式传递不同的参数
            const apiMode = this.currentMode === 'intelligent' ? 'intelligent' : 'chat';

            // 对话模式下根据深度思考状态决定是否启用思考功能
            const enableThinking = this.currentMode === 'chat' ? this.deepThinkingEnabled : true;

            response = await this.api.chat(message, this.conversationHistory, apiMode, this.currentImage, (chunk, type) => {
                // 流式输出回调
                if (type === 'reasoning') {
                    // 思考过程
                    reasoningContent += chunk;
                } else {
                    // 主要内容
                    streamingContent += chunk;
                }

                if (!streamingMessageDiv) {
                    // 创建流式消息容器
                    streamingMessageDiv = this.createStreamingMessage();
                }

                // 更新流式消息内容
                this.updateStreamingMessage(streamingMessageDiv, streamingContent, reasoningContent);
            }, enableThinking);

            // 流式输出完成后，移除streaming类并保存完整内容
            let finalContent = response.content || response;

            if (streamingMessageDiv) {
                streamingMessageDiv.classList.remove('streaming');

                // 移除流式光标效果
                const streamingCursor = streamingMessageDiv.querySelector('.streaming-cursor');
                if (streamingCursor) {
                    streamingCursor.remove();
                }

                // 如果是对话模式且有思考过程，保存完整的HTML结构
                if (this.currentMode === 'chat' && reasoningContent && reasoningContent.trim()) {
                    const messageText = streamingMessageDiv.querySelector('.message-text');
                    if (messageText) {
                        finalContent = messageText.innerHTML;
                    }
                }

                // 强制刷新显示确保消息可见
                setTimeout(() => {
                    this.ensureMessageVisible(streamingMessageDiv);
                }, 100);
            } else {
                // 如果没有流式输出（可能是错误情况），直接添加消息
                this.addMessage('assistant', finalContent);
            }

            // 更新对话历史
            this.conversationHistory.push(
                { role: 'user', content: message },
                { role: 'assistant', content: finalContent }
            );

            console.log('📚 更新后对话历史长度:', this.conversationHistory.length);

            // 限制历史记录长度（保留最近10轮对话，即20条消息）
            if (this.conversationHistory.length > 20) {
                this.conversationHistory = this.conversationHistory.slice(-20);
                console.log('✂️ 对话历史已截断到20条消息');
            }

            // 保存对话历史到本地存储
            this.saveConversationHistory();

        } catch (error) {
            console.error('发送消息失败:', error);
            this.addMessage('assistant', `❌ 抱歉，消息发送失败: ${error.message}`);
        } finally {
            this.setProcessing(false);
        }
    }

    /**
     * 创建流式消息容器
     */
    createStreamingMessage() {
        const messagesContainer = this.elements.chatMessages;
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant streaming';

        const now = new Date();
        const time = now.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        const avatar = this.currentMode === 'intelligent' ? '🤖' : '💬';

        // 创建基本的消息结构，包含一个可见的占位符
        messageDiv.innerHTML = `
            <div class="message-avatar">${avatar}</div>
            <div class="message-content">
                <div class="message-text">
                    <div class="streaming-placeholder" style="color: #666; font-style: italic;">正在思考中...</div>
                </div>
                <div class="message-time">${time}</div>
            </div>
        `;

        messagesContainer.appendChild(messageDiv);

        // 立即显示消息并滚动
        messageDiv.style.opacity = '1';
        messageDiv.style.transform = 'translateY(0)';
        this.ensureMessageVisible(messageDiv);

        return messageDiv;
    }

    /**
     * 更新流式消息内容
     */
    updateStreamingMessage(messageDiv, content, reasoningContent = '') {
        const messageText = messageDiv.querySelector('.message-text');
        if (!messageText) return;

        // 移除占位符（如果存在）
        const placeholder = messageText.querySelector('.streaming-placeholder');
        if (placeholder) {
            placeholder.remove();
        }

        // 初始化容器（只在第一次调用时）
        if (!messageText.dataset.initialized) {
            this.initializeStreamingContainers(messageText, reasoningContent, content);
            messageText.dataset.initialized = 'true';
        }

        // 更新思考过程内容
        if (reasoningContent && reasoningContent.trim()) {
            const thinkingText = messageText.querySelector('.thinking-text');
            const thinkingTimeSpan = messageText.querySelector('.thinking-time');

            if (thinkingText) {
                thinkingText.textContent = reasoningContent;
            }

            // 更新思考时间（使用真实时间）
            if (thinkingTimeSpan) {
                const thinkingStartTime = messageText.dataset.thinkingStartTime;
                if (thinkingStartTime) {
                    const thinkingTime = this.calculateThinkingTime(parseInt(thinkingStartTime));
                    thinkingTimeSpan.textContent = thinkingTime;
                }
            }
        }

        // 更新回答内容 - 实时显示
        if (content && content.trim()) {
            let answerText = messageText.querySelector('.answer-text');

            // 如果没有答案容器，创建一个简单的容器
            if (!answerText) {
                answerText = document.createElement('div');
                answerText.className = 'answer-text';
                answerText.style.cssText = 'font-size: 0.875rem; color: #374151; line-height: 1.6; white-space: pre-wrap;';
                messageText.appendChild(answerText);
            }

            // 实时更新内容，添加光标效果
            answerText.innerHTML = this.renderMarkdown(content) + '<span class="streaming-cursor">▋</span>';
        }

        // 立即滚动到最新消息，不使用节流
        this.ensureMessageVisible(messageDiv);
    }

    /**
     * 初始化流式消息容器
     */
    initializeStreamingContainers(messageText, reasoningContent, content) {
        let htmlContent = '';

        // 预先创建思考过程容器（如果启用了深度思考）
        if (this.currentMode === 'chat' && this.deepThinkingEnabled) {
            const thinkingTime = '0.0s'; // 初始时间
            const thinkingId = `thinking-${Date.now()}`;
            const thinkingStartTime = Date.now();

            // 记录思考开始时间
            messageText.dataset.thinkingStartTime = thinkingStartTime.toString();

            htmlContent += `
                <div class="thinking-section" id="${thinkingId}" style="margin-bottom: 16px;">
                    <div class="thinking-header" onclick="window.contentAuditorApp.toggleThinking('${thinkingId}')">
                        <div class="thinking-title">
                            <span>🧠</span>
                            <span>思考过程</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <span class="thinking-time">${thinkingTime}</span>
                            <span class="thinking-toggle">▼</span>
                        </div>
                    </div>
                    <div class="thinking-content">
                        <div style="padding: 16px;">
                            <pre class="thinking-text" style="white-space: pre-wrap; margin: 0;"></pre>
                        </div>
                    </div>
                </div>
            `;
        }

        // 预先创建回答内容容器（使用气泡样式）
        htmlContent += `
            <div class="answer-section" style="margin-bottom: 16px;">
                <div class="message-bubble assistant-bubble" style="border-radius: 18px; padding: 16px; position: relative; max-width: 85%;">
                    <div class="answer-text" style="font-size: 0.875rem; line-height: 1.6;"></div>
                </div>
            </div>
        `;

        // 一次性设置HTML内容
        messageText.innerHTML = htmlContent;
    }

    /**
     * 计算思考时间（基于实际时间）
     */
    calculateThinkingTime(startTime) {
        if (!startTime) return '0.0s';

        const elapsed = (Date.now() - startTime) / 1000;
        return `${elapsed.toFixed(1)}s`;
    }

    /**
     * 切换深度思考模式
     */
    toggleDeepThinking() {
        this.deepThinkingEnabled = !this.deepThinkingEnabled;
        this.updateThinkingButtonState();

        // 保存状态到本地存储
        localStorage.setItem('contentAuditor_deepThinking', this.deepThinkingEnabled);

        console.log('🧠 深度思考模式:', this.deepThinkingEnabled ? '已启用' : '已禁用');
    }

    /**
     * 更新深度思考按钮状态
     */
    updateThinkingButtonState() {
        const thinkingToggleBtn = document.getElementById('thinking-toggle-btn');
        const thinkingStatus = thinkingToggleBtn.querySelector('.thinking-status');

        if (this.deepThinkingEnabled) {
            thinkingToggleBtn.classList.add('active');
            thinkingStatus.textContent = '深度思考';
        } else {
            thinkingToggleBtn.classList.remove('active');
            thinkingStatus.textContent = '快速回答';
        }
    }

    /**
     * 加载深度思考状态
     */
    loadDeepThinkingState() {
        try {
            const saved = localStorage.getItem('contentAuditor_deepThinking');
            if (saved !== null) {
                this.deepThinkingEnabled = saved === 'true';
            }
            console.log('🧠 加载深度思考状态:', this.deepThinkingEnabled ? '已启用' : '已禁用');
        } catch (error) {
            console.warn('⚠️ 加载深度思考状态失败:', error);
            this.deepThinkingEnabled = true; // 默认启用
        }
    }

    /**
     * 切换思考过程的展开/折叠状态
     */
    toggleThinking(thinkingId) {
        const thinkingSection = document.getElementById(thinkingId);
        if (thinkingSection) {
            const thinkingContent = thinkingSection.querySelector('.thinking-content');
            const toggle = thinkingSection.querySelector('.thinking-toggle');
            const isExpanded = thinkingContent.style.maxHeight && thinkingContent.style.maxHeight !== '0px';

            if (isExpanded) {
                // 折叠
                thinkingContent.style.maxHeight = '0px';
                thinkingContent.style.padding = '0';
                toggle.textContent = '▼';
                toggle.style.transform = 'rotate(0deg)';
            } else {
                // 展开
                thinkingContent.style.maxHeight = '600px';
                thinkingContent.style.padding = '';
                toggle.textContent = '▲';
                toggle.style.transform = 'rotate(180deg)';
            }
        }
    }

    /**
     * 添加消息到聊天界面
     */
    addMessage(role, content) {
        const messagesContainer = this.elements.chatMessages;
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;

        const now = new Date();
        const time = now.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        const avatar = role === 'user' ? '👤' : (this.currentMode === 'intelligent' ? '🤖' : '💬');

        try {
            // 检查内容是否包含思考过程结构
            const hasThinkingSection = content.includes('thinking-section') || content.includes('🧠 思考过程');

            let messageContent = content;
            if (!hasThinkingSection && !content.includes('<div') && !content.includes('<span') && 
                !content.includes('<strong>') && !content.includes('<br>') && !content.includes('<em>')) {
                // 如果不是HTML格式，则使用markdown渲染
                messageContent = this.renderMarkdown(content);
            }

            // 统一消息结构
            messageDiv.innerHTML = `
                <div class="message-avatar">${avatar}</div>
                <div class="message-content">
                    <div class="message-text">
                        ${role === 'assistant' ? 
                            `<div class="message-bubble assistant-bubble">${messageContent}</div>` : 
                            messageContent
                        }
                    </div>
                    <div class="message-time">${time}</div>
                </div>
            `;

            // 如果是助手消息且包含思考过程，绑定事件
            if (role === 'assistant' && hasThinkingSection) {
                setTimeout(() => {
                    const thinkingSections = messageDiv.querySelectorAll('.thinking-section');
                    thinkingSections.forEach(section => {
                        const header = section.querySelector('.thinking-header');
                        if (header && !header.onclick) {
                            const thinkingId = section.id;
                            header.onclick = () => window.contentAuditorApp.toggleThinking(thinkingId);
                        }
                    });
                }, 0);
            }
        } catch (error) {
            console.error('消息渲染失败:', error);
            // 降级处理：显示原始内容
            messageDiv.innerHTML = `
                <div class="message-avatar">${avatar}</div>
                <div class="message-content">
                    <div class="message-text">${this.escapeHtml(content)}</div>
                    <div class="message-time">${time}</div>
                </div>
            `;
        }

        messagesContainer.appendChild(messageDiv);

        // 强制刷新显示并滚动到最新消息
        this.ensureMessageVisible(messageDiv);

        // 添加动画效果
        messageDiv.classList.add('fade-in');

        return messageDiv;
    }

    /**
     * 确保消息可见并滚动到最新位置
     */
    ensureMessageVisible(messageDiv) {
        const messagesContainer = this.elements.chatMessages;

        // 强制重新计算布局
        messagesContainer.offsetHeight;

        // 使用 requestAnimationFrame 确保 DOM 更新完成后再滚动
        requestAnimationFrame(() => {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            // 如果滚动没有生效，再次尝试
            setTimeout(() => {
                if (messagesContainer.scrollTop < messagesContainer.scrollHeight - messagesContainer.clientHeight - 10) {
                    messagesContainer.scrollTop = messagesContainer.scrollHeight;
                }
            }, 100);
        });
    }

    /**
     * 强制刷新聊天界面显示
     */
    forceRefreshChatDisplay() {
        const messagesContainer = this.elements.chatMessages;

        // 强制重新渲染
        const display = messagesContainer.style.display;
        messagesContainer.style.display = 'none';
        messagesContainer.offsetHeight; // 触发重排
        messagesContainer.style.display = display;

        // 滚动到底部
        setTimeout(() => {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }, 50);
    }

    /**
     * 渲染Markdown内容
     */
    renderMarkdown(content) {
        try {
            // 检查是否有marked库
            if (typeof marked !== 'undefined') {
                // 配置marked选项
                marked.setOptions({
                    breaks: true,        // 支持换行
                    gfm: true,          // 支持GitHub风格markdown
                    sanitize: false,    // 允许HTML（因为我们信任内容）
                    smartLists: true,   // 智能列表
                    smartypants: true   // 智能标点
                });

                return marked.parse(content);
            } else {
                // 如果没有marked库，进行简单的格式化
                return this.simpleMarkdownRender(content);
            }
        } catch (error) {
            console.warn('Markdown渲染失败，使用简单格式化:', error);
            return this.simpleMarkdownRender(content);
        }
    }

    /**
     * 简单的Markdown渲染（备用方案）
     */
    simpleMarkdownRender(content) {
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')  // 粗体
            .replace(/\*(.*?)\*/g, '<em>$1</em>')              // 斜体
            .replace(/`(.*?)`/g, '<code>$1</code>')            // 行内代码
            .replace(/^### (.*$)/gm, '<h3>$1</h3>')           // 三级标题
            .replace(/^## (.*$)/gm, '<h2>$1</h2>')            // 二级标题
            .replace(/^# (.*$)/gm, '<h1>$1</h1>')             // 一级标题
            .replace(/^- (.*$)/gm, '<li>$1</li>')             // 列表项
            .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')        // 包装列表
            .replace(/\n/g, '<br>');                          // 换行
    }

    /**
     * HTML转义函数
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 处理特殊命令
     */
    async handleSpecialCommands(message) {
        const lowerMessage = message.toLowerCase();

        // 魔搭创空间API密钥设置
        if (lowerMessage.startsWith('设置魔搭密钥') || lowerMessage.startsWith('设置modelscope密钥')) {
            const apiKey = message.split(' ').slice(-1)[0];
            if (apiKey && apiKey.length > 10) {
                this.api.setModelScopeApiKey(apiKey);
                this.updateConnectionStatus(); // 更新连接状态
                this.addMessage('assistant', '✅ 魔搭创空间API密钥设置成功！现在可以使用AI分析功能了。');
            } else {
                this.addMessage('assistant', '❌ 魔搭创空间API密钥格式不正确，请检查后重试。');
            }
            return true;
        }

        // 硅基流动API密钥设置
        if (lowerMessage.startsWith('设置硅基密钥') || lowerMessage.startsWith('设置siliconflow密钥')) {
            const apiKey = message.split(' ').slice(-1)[0];
            if (apiKey && apiKey.length > 10) {
                this.api.setSiliconFlowApiKey(apiKey);
                this.addMessage('assistant', '✅ 硅基流动API密钥设置成功！现在可以使用语音识别功能了。');
            } else {
                this.addMessage('assistant', '❌ 硅基流动API密钥格式不正确，请检查后重试。');
            }
            return true;
        }

        // 兼容旧的API密钥设置命令
        if (lowerMessage.startsWith('设置api密钥') || lowerMessage.startsWith('设置 api密钥')) {
            const apiKey = message.split(' ').slice(-1)[0];
            if (apiKey && apiKey.length > 10) {
                this.api.setModelScopeApiKey(apiKey);
                this.updateConnectionStatus(); // 更新连接状态
                this.addMessage('assistant', '✅ 魔搭创空间API密钥设置成功！现在可以使用AI分析功能了。\n\n💡 提示：建议使用更明确的命令 "设置魔搭密钥 your-key"');
            } else {
                this.addMessage('assistant', '❌ API密钥格式不正确，请检查后重试。');
            }
            return true;
        }

        // API密钥帮助
        if (lowerMessage.includes('api密钥') && (lowerMessage.includes('帮助') || lowerMessage.includes('配置'))) {
            this.addMessage('assistant', `
                🔑 <strong>API密钥配置帮助</strong><br><br>

                <strong>📋 支持的命令：</strong><br>
                • <code>设置魔搭密钥 your-modelscope-key</code> - 设置魔搭创空间API密钥<br>
                • <code>设置硅基密钥 your-siliconflow-key</code> - 设置硅基流动API密钥<br><br>

                <strong>🔗 获取API密钥：</strong><br>
                <strong>魔搭创空间（必需）：</strong><br>
                1. 访问 <a href="https://modelscope.cn/" target="_blank">魔搭创空间</a><br>
                2. 注册并登录账号<br>
                3. 进入控制台获取API密钥<br><br>

                <strong>硅基流动（可选）：</strong><br>
                1. 访问 <a href="https://siliconflow.cn/" target="_blank">硅基流动</a><br>
                2. 注册并登录账号<br>
                3. 获取API密钥（用于语音识别功能）<br><br>

                <strong>注意事项：</strong><br>
                • API密钥会保存在浏览器本地存储中<br>
                • 请妥善保管您的API密钥<br>
                • 魔搭创空间密钥用于主要AI功能<br>
                • 硅基流动密钥用于语音识别功能（可选）
            `);
            return true;
        }

        // 环境变量检测
        if (lowerMessage.includes('环境变量') && (lowerMessage.includes('检测') || lowerMessage.includes('诊断') || lowerMessage.includes('状态'))) {
            const envStatus = this.checkEnvironmentVariables();
            this.addMessage('assistant', `
                🔍 <strong>环境变量检测报告</strong><br><br>

                <strong>📊 检测结果：</strong><br>
                ${envStatus.details}<br>

                <strong>💡 说明：</strong><br>
                • <strong>localStorage</strong>: 用户手动设置的API密钥<br>
                • <strong>process.env</strong>: Node.js环境变量（静态网站通常不支持）<br>
                • <strong>window全局变量</strong>: 服务端注入的全局变量<br>
                • <strong>URL参数</strong>: 通过URL传递的临时密钥<br>
                • <strong>meta标签</strong>: 通过HTML meta标签注入的密钥<br><br>

                <strong>🔧 建议：</strong><br>
                ${envStatus.hasEnvSupport ?
                    '✅ 检测到环境变量支持，请确保在创空间设置中正确配置了 <code>MODELSCOPE_API_KEY</code> 环境变量。' :
                    '❌ 未检测到环境变量支持，请使用手动配置方式：<code>设置API密钥 your-key</code>'
                }
            `);
            return true;
        }

        // 清空对话
        if (lowerMessage.includes('清空') || lowerMessage.includes('重置') || lowerMessage.includes('清除')) {
            this.resetConversation();
            return true;
        }

        return false;
    }

    /**
     * 处理快捷操作
     */
    async handleQuickAction(action) {
        if (!this.api.isApiKeyAvailable()) {
            this.promptForApiKey();
            return;
        }

        switch (action) {
            case 'enhanced_review':
                this.handleEnhancedReview();
                break;
            case 'law_search':
                this.handleLawSearch();
                break;
            case 'rewrite':
                this.handleRewrite();
                break;
            case 'industry_guide':
                this.handleIndustryGuide();
                break;
        }
    }

    /**
     * 强化复查
     */
    async handleEnhancedReview() {
        const text = this.elements.textInput.value.trim();
        if (!text && !this.currentImage) {
            this.addMessage('assistant', '请先在左侧输入需要复查的文案内容或上传图片。');
            return;
        }

        // 显示用户输入的快捷指令
        this.addMessage('user', '快捷指令——强化复查');

        this.setProcessing(true);

        try {
            const response = await this.api.handleQuickAction('enhanced_review', text, this.currentImage);
            this.addMessage('assistant', response);
        } catch (error) {
            this.addMessage('assistant', `❌ 强化复查失败: ${error.message}`);
        } finally {
            this.setProcessing(false);
        }
    }

    /**
     * 法规搜索
     */
    async handleLawSearch() {
        // 显示用户输入的快捷指令
        this.addMessage('user', '快捷指令——法规索引');

        this.setProcessing(true);

        try {
            const response = await this.api.handleQuickAction('law_search');
            this.addMessage('assistant', response);
        } catch (error) {
            this.addMessage('assistant', `❌ 法规索引失败: ${error.message}`);
        } finally {
            this.setProcessing(false);
        }
    }

    /**
     * 文案重写
     */
    async handleRewrite() {
        const text = this.elements.textInput.value.trim();
        if (!text && !this.currentImage) {
            this.addMessage('assistant', '请先在左侧输入需要重写的文案内容或上传图片。');
            return;
        }

        // 显示用户输入的快捷指令
        this.addMessage('user', '快捷指令——文案重写');

        this.setProcessing(true);

        try {
            const response = await this.api.handleQuickAction('rewrite', text, this.currentImage);
            this.addMessage('assistant', response);
        } catch (error) {
            this.addMessage('assistant', `❌ 文案重写失败: ${error.message}`);
        } finally {
            this.setProcessing(false);
        }
    }

    /**
     * 行业指南
     */
    async handleIndustryGuide() {
        // 显示用户输入的快捷指令
        this.addMessage('user', '快捷指令——行业指南');

        this.setProcessing(true);

        try {
            const response = await this.api.handleQuickAction('industry_guide');
            this.addMessage('assistant', response);
        } catch (error) {
            this.addMessage('assistant', `❌ 行业指南失败: ${error.message}`);
        } finally {
            this.setProcessing(false);
        }
    }



    /**
     * 保存对话历史到本地存储
     */
    saveConversationHistory() {
        try {
            const historyData = {
                history: this.conversationHistory,
                timestamp: Date.now(),
                mode: this.currentMode
            };
            localStorage.setItem('contentAuditor_conversationHistory', JSON.stringify(historyData));
            console.log('💾 对话历史已保存到本地存储');
        } catch (error) {
            console.warn('⚠️ 保存对话历史失败:', error);
        }
    }

    /**
     * 从本地存储加载对话历史
     */
    loadConversationHistory() {
        try {
            const savedData = localStorage.getItem('contentAuditor_conversationHistory');
            if (savedData) {
                const historyData = JSON.parse(savedData);

                // 检查数据是否过期（24小时）或包含旧的raw模式
                const isExpired = Date.now() - historyData.timestamp > 24 * 60 * 60 * 1000;
                const hasOldRawMode = historyData.mode === 'raw';

                if (!isExpired && !hasOldRawMode && historyData.history && Array.isArray(historyData.history)) {
                    this.conversationHistory = historyData.history;
                    console.log('📚 已加载对话历史:', this.conversationHistory.length, '条消息');

                    // 恢复保存的模式（如果有的话）
                    if (historyData.mode && (historyData.mode === 'intelligent' || historyData.mode === 'chat')) {
                        this.currentMode = historyData.mode;
                        console.log('🔄 恢复保存的模式:', this.currentMode);

                        // 更新界面模式状态
                        this.updateModeUI();
                    }

                    // 恢复对话界面
                    this.restoreConversationUI();
                } else {
                    if (hasOldRawMode) {
                        console.log('🔄 检测到旧的raw模式数据，清理并重新开始');
                        localStorage.removeItem('contentAuditor_conversationHistory');
                    } else {
                        console.log('🕒 对话历史已过期或无效，重新开始');
                    }
                    this.conversationHistory = [];
                }
            }
        } catch (error) {
            console.warn('⚠️ 加载对话历史失败:', error);
            this.conversationHistory = [];
        }
    }

    /**
     * 恢复对话界面
     */
    restoreConversationUI() {
        if (this.conversationHistory.length === 0) return;

        console.log('🔄 恢复对话界面...');

        // 清空当前消息
        this.elements.chatMessages.innerHTML = '';

        // 重新显示历史消息
        for (let i = 0; i < this.conversationHistory.length; i += 2) {
            const userMessage = this.conversationHistory[i];
            const assistantMessage = this.conversationHistory[i + 1];

            if (userMessage && userMessage.role === 'user') {
                this.addMessage('user', userMessage.content);
            }

            if (assistantMessage && assistantMessage.role === 'assistant') {
                this.addMessage('assistant', assistantMessage.content);
            }
        }

        // 强制刷新显示并确保最后一条消息可见
        setTimeout(() => {
            this.forceRefreshChatDisplay();
        }, 100);

        console.log('✅ 对话界面恢复完成');
    }

    /**
     * 重置对话
     */
    resetConversation() {
        this.conversationHistory = [];
        this.elements.chatMessages.innerHTML = '';

        // 清除本地存储的对话历史
        localStorage.removeItem('contentAuditor_conversationHistory');

        // 根据当前模式显示相应的欢迎消息
        if (this.currentMode === 'intelligent') {
            this.showIntelligentWelcome();
        } else {
            this.showChatWelcome();
        }

        console.log('🔄 对话已重置');
    }

    /**
     * 切换主题
     */
    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem(this.config.storage.prefix + this.config.storage.keys.theme, newTheme);

        // 更新主题按钮图标
        const themeIcon = document.getElementById('theme-icon');
        themeIcon.textContent = newTheme === 'dark' ? '☀️' : '🌙';

        this.showToast(`已切换到${newTheme === 'dark' ? '暗色' : '亮色'}主题`, 'success');
    }

    /**
     * 加载主题
     */
    loadTheme() {
        const savedTheme = localStorage.getItem(this.config.storage.prefix + this.config.storage.keys.theme);
        const theme = savedTheme || this.config.ui.theme.default;

        document.documentElement.setAttribute('data-theme', theme);

        const themeIcon = document.getElementById('theme-icon');
        if (themeIcon) {
            themeIcon.textContent = theme === 'dark' ? '☀️' : '🌙';
        }
    }

    /**
     * 显示帮助
     */
    showHelp() {
        this.elements.helpModal.classList.add('show');
    }

    /**
     * 隐藏帮助
     */
    hideHelp() {
        this.elements.helpModal.classList.remove('show');
    }

    /**
     * 显示功能排期
     */
    showRoadmap() {
        this.elements.roadmapModal.classList.add('show');
    }

    /**
     * 隐藏功能排期
     */
    hideRoadmap() {
        this.elements.roadmapModal.classList.remove('show');
    }

    /**
     * 显示提示
     */
    showToast(message, type = 'info') {
        this.elements.toast.textContent = message;
        this.elements.toast.className = `toast ${type} show`;

        setTimeout(() => {
            this.elements.toast.classList.remove('show');
        }, this.config.ui.toast.duration);
    }

    // 旧的进度管理函数已被 ProgressManager 类替代
    // 旧的步骤更新和移除函数已被 ProgressManager 类替代
    // 旧的计时器函数已被 ProgressManager 类替代

    // 旧的图片分析进度函数已被 ProgressManager 类替代

    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 处理键盘快捷键
     */
    handleKeyboardShortcuts(event) {
        // Ctrl/Cmd + Enter 发送消息
        if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
            if (document.activeElement === this.elements.chatInput) {
                this.sendMessage();
            }
        }

        // Esc 关闭模态框
        if (event.key === 'Escape') {
            this.hideHelp();
            this.hideRoadmap();
        }
    }

    /**
     * 切换语音输入
     */
    toggleVoiceInput() {
        if (this.isRecording) {
            this.stopVoiceInput();
        } else {
            this.startVoiceInput();
        }
    }

    /**
     * 开始语音输入
     */
    async startVoiceInput() {
        try {
            // 检查浏览器支持
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                throw new Error('您的浏览器不支持语音录制功能');
            }

            // 请求麦克风权限
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            });

            // 初始化录音器
            this.mediaRecorder = new MediaRecorder(stream, {
                mimeType: 'audio/webm;codecs=opus'
            });

            this.audioChunks = [];

            // 录音数据处理
            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.audioChunks.push(event.data);
                }
            };

            // 录音结束处理
            this.mediaRecorder.onstop = async () => {
                try {
                    const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });
                    await this.processVoiceInput(audioBlob);
                } catch (error) {
                    console.error('语音处理失败:', error);
                    this.showToast('语音处理失败: ' + error.message, 'error');
                } finally {
                    this.cleanupVoiceInput();
                }
            };

            // 开始录音
            this.mediaRecorder.start();
            this.isRecording = true;

            // 更新UI
            this.updateVoiceInputUI(true);
            this.showToast('开始录音，再次点击停止', 'info');

        } catch (error) {
            console.error('启动语音输入失败:', error);
            this.showToast('启动语音输入失败: ' + error.message, 'error');
            this.cleanupVoiceInput();
        }
    }

    /**
     * 停止语音输入
     */
    stopVoiceInput() {
        if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
            this.mediaRecorder.stop();
        }

        // 停止所有音频轨道
        if (this.mediaRecorder && this.mediaRecorder.stream) {
            this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
        }

        this.isRecording = false;
        this.updateVoiceInputUI(false);
        this.showToast('录音已停止，正在处理...', 'info');
    }

    /**
     * 处理语音输入
     */
    async processVoiceInput(audioBlob) {
        try {
            // 显示处理进度
            const progressMessage = this.addMessage('assistant', '🎤 正在处理语音，请稍候...');

            // 调用语音转文字API
            const transcribedText = await this.api.transcribeAudio(audioBlob);

            // 移除进度消息
            const progressElement = document.getElementById(progressMessage);
            if (progressElement) {
                progressElement.remove();
            }

            if (transcribedText && transcribedText.trim()) {
                // 将转录的文字填入输入框
                this.elements.chatInput.value = transcribedText.trim();
                this.showToast('语音识别成功！', 'success');

                // 自动聚焦到输入框
                this.elements.chatInput.focus();
            } else {
                this.showToast('未识别到语音内容，请重试', 'warning');
            }

        } catch (error) {
            console.error('语音转文字失败:', error);

            if (error.message.includes('SiliconFlow API密钥')) {
                this.showToast('请先配置语音识别API密钥', 'warning');
                this.promptForSiliconFlowApiKey();
            } else {
                this.showToast('语音识别失败: ' + error.message, 'error');
            }
        }
    }

    /**
     * 更新语音输入UI
     */
    updateVoiceInputUI(isRecording) {
        const voiceBtn = this.elements.voiceInputBtn;
        if (voiceBtn) {
            if (isRecording) {
                voiceBtn.classList.add('recording');
                voiceBtn.innerHTML = '🔴';
                voiceBtn.title = '点击停止录音';
            } else {
                voiceBtn.classList.remove('recording');
                voiceBtn.innerHTML = '🎤';
                voiceBtn.title = '点击开始语音输入';
            }
        }
    }

    /**
     * 清理语音输入资源
     */
    cleanupVoiceInput() {
        this.isRecording = false;
        this.audioChunks = [];

        if (this.mediaRecorder) {
            if (this.mediaRecorder.stream) {
                this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
            }
            this.mediaRecorder = null;
        }

        this.updateVoiceInputUI(false);
    }

    /**
     * 提示输入SiliconFlow API密钥
     */
    promptForSiliconFlowApiKey() {
        const apiKey = prompt('请输入您的SiliconFlow API密钥（用于语音识别功能）：');
        if (apiKey && apiKey.trim()) {
            this.api.setSiliconFlowApiKey(apiKey.trim());
            this.showToast('SiliconFlow API密钥已保存', 'success');
        }
    }

    /**
     * 销毁应用实例
     */
    destroy() {
        if (this.isDestroyed) return;

        console.log('🧹 销毁应用实例...');
        this.isDestroyed = true;

        // 清理进度管理器
        if (this.progressManager) {
            this.progressManager.destroy();
        }

        // 清理语音输入
        this.cleanupVoiceInput();

        // 清理API实例
        if (this.api && typeof this.api.destroy === 'function') {
            this.api.destroy();
        }

        // 清空数据
        this.conversationHistory = [];
        this.currentImage = null;
        this.isProcessing = false;

        console.log('✅ 应用实例已销毁');
    }
}

// 旧的全局函数已移除，现在使用类方法 window.contentAuditorApp.toggleThinking()

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.contentAuditorApp = new ContentAuditorApp();

    // 页面卸载时清理资源
    window.addEventListener('beforeunload', () => {
        if (window.contentAuditorApp) {
            window.contentAuditorApp.destroy();
        }
    });

    // 页面隐藏时执行清理（移动端）
    document.addEventListener('visibilitychange', () => {
        if (document.hidden && window.memoryManager) {
            window.memoryManager.performCleanup();
        }
    });
});

// 导出到全局作用域
window.ContentAuditorApp = ContentAuditorApp;
