<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>内容审核管家</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'fade-in': 'fadeIn 0.3s ease-in-out',
                        'slide-down': 'slideDown 0.3s ease-out',
                        'pulse-soft': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0', transform: 'translateY(-10px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        slideDown: {
                            '0%': { maxHeight: '0', opacity: '0' },
                            '100%': { maxHeight: '600px', opacity: '1' }
                        }
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="static/css/style.css?v=3.0">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔍</text></svg>">
</head>
<body>
    <!-- 页面加载动画 -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">内容审核管家启动中...</div>
    </div>

    <!-- 主容器 -->
    <div class="container">
        <!-- 头部导航 -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <span class="logo-icon">🔍</span>
                    <h1 class="logo-text">内容审核管家</h1>
                    <span class="logo-subtitle">清源小队出品</span>
                </div>
                <div class="header-actions">
                    <button class="btn btn-outline" id="theme-toggle" title="切换主题">
                        <span class="icon" id="theme-icon">🌙</span>
                    </button>
                    <button class="btn btn-outline" id="roadmap-btn">
                        <span class="icon">🗓️</span>
                        功能排期
                    </button>
                    <button class="btn btn-outline" id="help-btn">
                        <span class="icon">❓</span>
                        使用帮助
                    </button>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 左侧输入区域 -->
            <div class="input-section">
                <!-- 标签页切换 -->
                <div class="tab-container">
                    <div class="tab-buttons">
                        <button class="tab-btn active" data-tab="text">
                            <span class="icon">📝</span>
                            文案审核
                        </button>
                        <button class="tab-btn" data-tab="image">
                            <span class="icon">🖼️</span>
                            图片审核
                        </button>
                    </div>
                </div>

                <!-- 文案审核面板 -->
                <div class="tab-content active" id="text-tab">
                    <div class="input-group">
                        <label for="text-input" class="input-label">
                            <span class="icon">📝</span>
                            输入待审核文案
                        </label>
                        <textarea
                            id="text-input"
                            class="text-input"
                            placeholder="请输入需要审核的文案内容...&#10;&#10;💡 支持直接输入或上传文档（TXT、DOC、PDF等格式）"
                        ></textarea>
                        <div class="input-footer">
                            <span class="char-count">0 / 5000</span>
                            <div class="input-actions">
                                <button class="btn btn-secondary" id="upload-document" title="支持上传多种文档格式：TXT、DOC、DOCX、PDF、RTF、ODT、CSV、XLSX、XLS、PPTX、MD、HTML">
                                    <span class="icon">📄</span>
                                    上传文档
                                </button>
                                <button class="btn btn-secondary" id="clear-text" title="清空当前输入的所有内容">
                                    <span class="icon">🗑️</span>
                                    清空
                                </button>
                            </div>
                            <input type="file" id="document-input" accept=".txt,.docx,.doc,.pdf,.rtf,.odt,.csv,.xlsx,.xls,.pptx,.ppt,.md,.html,.htm" style="display: none;">
                        </div>
                    </div>

                    <!-- 智能切片预览 -->
                    <div class="preview-section" id="text-preview">
                        <div class="preview-header">
                            <span class="icon">✂️</span>
                            智能切片预览（开发中）
                            <div class="preview-controls">
                                <button class="btn btn-outline btn-sm" id="toggle-preview-mode" title="切换预览模式">
                                    <span class="icon">📄</span>
                                    <span id="preview-mode-text">全段落预览</span>
                                </button>
                            </div>
                        </div>
                        <div class="preview-content" id="preview-content">
                            <div class="preview-placeholder">
                                输入文案后查看智能切片预览
                            </div>
                        </div>
                    </div>

                    <button class="btn btn-primary btn-large" id="analyze-text">
                        <span class="icon">🔍</span>
                        一键分析
                    </button>
                </div>

                <!-- 图片审核面板 -->
                <div class="tab-content" id="image-tab">
                    <div class="upload-area" id="upload-area">
                        <div class="upload-content">
                            <div class="upload-icon">📁</div>
                            <div class="upload-text">
                                <div class="upload-title">点击上传或拖拽图片到此处</div>
                                <div class="upload-subtitle">支持 JPG、PNG、WEBP 等格式，最大 50MB</div>
                            </div>
                            <input type="file" id="file-input" accept="image/*" multiple hidden>
                        </div>
                    </div>

                    <!-- 图片预览 -->
                    <div class="image-preview" id="image-preview" style="display: none;">
                        <div class="image-container">
                            <img id="preview-image" src="" alt="预览图片">
                            <button class="remove-image" id="remove-image">×</button>
                        </div>
                        <div class="image-info">
                            <div class="image-name" id="image-name"></div>
                            <div class="image-size" id="image-size"></div>
                        </div>
                    </div>

                    <!-- 图片文案输入 -->
                    <div class="input-group" id="image-text-group" style="display: none;">
                        <label for="image-text-input" class="input-label">
                            <span class="icon">📝</span>
                            配套文案（可选）
                        </label>
                        <textarea
                            id="image-text-input"
                            class="text-input"
                            placeholder="输入配套文案内容..."
                        ></textarea>
                    </div>

                    <div class="button-group" id="image-buttons" style="display: none;">
                        <button class="btn btn-primary btn-large" id="analyze-image">
                            <span class="icon">🔍</span>
                            一键分析
                        </button>
                        <button class="btn btn-secondary btn-large" id="analyze-image-text" style="display: none;">
                            <span class="icon">🔍</span>
                            图文综合分析
                        </button>
                    </div>
                </div>
            </div>

            <!-- 右侧结果区域 -->
            <div class="result-section">
                <!-- 智能体模式界面 -->
                <div class="chat-container intelligent-mode" id="intelligent-chat">
                    <div class="chat-header">
                        <div class="chat-title">
                            <span class="icon">🤖</span>
                            AI智能助手
                        </div>
                        <div class="chat-header-actions">
                            <!-- 模式切换按钮 -->
                            <div class="mode-switch-container">
                                <div class="mode-labels">
                                    <span class="mode-label active" data-mode="intelligent">🤖 智能体模式 <small>【测试版】</small></span>
                                    <span class="mode-label" data-mode="chat">💬 对话模式 <small>【主力】</small></span>
                                </div>
                            </div>
                            <div class="chat-status" id="chat-status">
                                <span class="status-dot online"></span>
                                AI已连接
                            </div>
                            <!-- 深度思考控制按钮（仅对话模式显示） -->
                            <button class="btn btn-outline btn-sm thinking-toggle-btn" id="thinking-toggle-btn" onclick="window.contentAuditorApp.toggleDeepThinking()" title="切换深度思考模式" style="display: none;">
                                <span class="icon">🧠</span>
                                <span class="thinking-status">深度思考</span>
                            </button>
                            <button class="btn btn-outline btn-sm" onclick="window.contentAuditorApp.resetConversation()" title="重置对话">
                                <span class="icon">🔄</span>
                                重置
                            </button>
                        </div>
                    </div>

                    <!-- 聊天消息区域 -->
                    <div class="chat-messages" id="chat-messages">
                        <div class="message assistant">
                            <div class="message-avatar">🤖</div>
                            <div class="message-content">
                                <div class="message-text">
                                    👋 <strong>欢迎使用内容审核管家智能体模式！</strong><br><br>
                                    🎯 我是您的专业内容合规审核助手，提供<strong>句子级精确定位</strong>和<strong>四色风险标记</strong>。<br>
                                    📝 请在左侧输入文案或上传图片，点击分析按钮开始审核，也可使用下方快捷指令获得专业建议。<br>
                                    🚀 让我们开始为您的内容保驾护航！
                                </div>
                                <div class="message-time">刚刚</div>
                            </div>
                        </div>
                    </div>

                    <!-- 快捷指令按钮 -->
                    <div class="quick-actions">
                        <div class="quick-actions-header">快捷指令</div>
                        <div class="quick-buttons">
                            <button class="quick-btn" data-action="enhanced_review">
                                <span class="icon">🔍</span>
                                强化复查
                            </button>
                            <button class="quick-btn" data-action="law_search">
                                <span class="icon">📖</span>
                                法规索引
                            </button>
                            <button class="quick-btn" data-action="rewrite">
                                <span class="icon">✏️</span>
                                重写文案
                            </button>
                            <button class="quick-btn" data-action="industry_guide">
                                <span class="icon">🏢</span>
                                行业指南
                            </button>
                        </div>
                    </div>

                    <!-- 消息输入区 -->
                    <div class="chat-input-container">
                        <div class="chat-input-wrapper">
                            <div class="chat-input-group">
                                <textarea
                                    id="chat-input"
                                    class="chat-input"
                                    placeholder="输入您的问题，获得专业合规建议...&#10;&#10;💡 可询问文案合规性、修改建议、法规解读等"
                                ></textarea>
                                <div class="chat-input-actions">
                                    <button class="chat-action-btn chat-action-text-btn" id="voice-input-chat" title="语音输入 - 支持中文语音识别（研发中）">
                                        <span class="voice-icon">🎤</span>
                                        <span class="voice-text">语音输入(研发中)</span>
                                        <span class="voice-loading" style="display: none;">
                                            <span class="loading-dots">
                                                <span></span>
                                                <span></span>
                                                <span></span>
                                            </span>
                                            转换中...
                                        </span>
                                    </button>
                                </div>
                            </div>
                            <button class="send-btn" id="send-btn">
                                发送
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 功能排期弹窗 -->
    <div class="modal" id="roadmap-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🗓️ 功能排期 & 开发计划</h3>
                <button class="modal-close" id="close-roadmap">&times;</button>
            </div>
            <div class="modal-body">
                <div class="help-section">
                    <h4>🚀 即将上线功能</h4>
                    <div class="roadmap-item">
                        <div class="roadmap-status developing">🔧 开发中</div>
                        <div class="roadmap-title">多Agent交互审查系统</div>
                        <div class="roadmap-desc">多个AI专家协同审核，提供更全面的合规建议</div>
                        <div class="roadmap-eta">即将推出</div>
                    </div>
                    <div class="roadmap-item">
                        <div class="roadmap-status planning">📋 规划中</div>
                        <div class="roadmap-title">智能语音识别</div>
                        <div class="roadmap-desc">支持语音输入，实时转换为文字进行审核</div>
                        <div class="roadmap-eta">开发排期中</div>
                    </div>
                    <div class="roadmap-item">
                        <div class="roadmap-status planning">📋 规划中</div>
                        <div class="roadmap-title">批量文档处理</div>
                        <div class="roadmap-desc">支持批量上传多个文档，一键完成合规审核</div>
                        <div class="roadmap-eta">开发排期中</div>
                    </div>
                </div>
                <div class="help-section">
                    <h4>💡 多Agent交互审查预告</h4>
                    <p>即将推出的<strong>多Agent交互审查系统</strong>将包含：</p>
                    <ul>
                        <li>🤖 <strong>法规专家Agent</strong>：专注广告法条文解读</li>
                        <li>🎯 <strong>营销专家Agent</strong>：优化文案表达效果</li>
                        <li>🔍 <strong>风险评估Agent</strong>：全面风险识别与评级</li>
                        <li>✏️ <strong>文案优化Agent</strong>：提供专业改写建议</li>
                        <li>🏢 <strong>行业专家Agent</strong>：针对特定行业的合规指导</li>
                    </ul>
                    <p>多个AI专家将协同工作，为您提供更专业、更全面的内容审核服务！</p>
                </div>
                <div class="help-section">
                    <h4>🎯 交互体验升级</h4>
                    <p>我们正在优化用户交互体验：</p>
                    <ul>
                        <li>🎨 更美观的界面设计</li>
                        <li>⚡ 更快速的响应速度</li>
                        <li>🔄 实时协作审核流程</li>
                        <li>📊 可视化审核报告</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div class="modal" id="help-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>使用帮助</h3>
                <button class="modal-close" id="close-help">&times;</button>
            </div>
            <div class="modal-body">
                <div class="help-section">
                    <h4>🔍 文案审核功能</h4>
                    <p>输入需要审核的文案内容，系统将进行<strong>智能切片预览（开发中）</strong>和<strong>风险检测</strong>，提供四级风险等级标识：</p>
                    <ul>
                        <li>🔴 <strong>严重风险</strong>：明显违反广告法，需要立即修改</li>
                        <li>🟠 <strong>警告风险</strong>：存在合规隐患，建议谨慎处理</li>
                        <li>🟣 <strong>注意风险</strong>：可能引起争议，建议优化表达</li>
                        <li>🟢 <strong>安全内容</strong>：符合规范，可以放心使用</li>
                    </ul>
                    <p>支持广告文案、营销文案、产品描述、宣传材料等多种内容类型。</p>
                </div>

                <div class="help-section">
                    <h4>🖼️ 图片审核功能</h4>
                    <p>上传图片进行内容审核，系统将分析：</p>
                    <ul>
                        <li>📝 <strong>图片中的文字内容</strong>：OCR识别并审核文字合规性</li>
                        <li>🎨 <strong>视觉元素</strong>：检测可能的违规图像内容</li>
                        <li>🔗 <strong>图文一致性</strong>：配套文案与图片内容的匹配度</li>
                    </ul>
                    <p>支持 JPG、PNG、BMP、WEBP、TIFF、SVG 等格式，最大 50MB。</p>
                </div>

                <div class="help-section">
                    <h4>📄 文档上传功能</h4>
                    <p>支持批量处理多种文档格式：</p>
                    <ul>
                        <li>📝 <strong>文本文档</strong>：TXT、DOC、DOCX、RTF、ODT</li>
                        <li>📊 <strong>表格文档</strong>：CSV、XLSX、XLS</li>
                        <li>📋 <strong>演示文档</strong>：PPTX、PPT</li>
                        <li>🌐 <strong>网页文档</strong>：HTML、HTM、MD</li>
                        <li>📖 <strong>PDF文档</strong>：PDF格式文件</li>
                    </ul>
                    <p>系统会自动提取文档中的文本内容进行合规审核。</p>
                </div>

                <div class="help-section">
                    <h4>🤖 智能体模式 <span style="color: #059669; font-size: 0.8em;">【主要迭代方向】</span></h4>
                    <p><strong>专业的结构化审核模式</strong>，提供标准化的合规审核服务：</p>

                    <div style="background: #f0f9ff; padding: 12px; border-radius: 8px; margin: 10px 0;">
                        <strong>🎯 核心特色</strong>：
                        <ul style="margin: 8px 0;">
                            <li>📋 <strong>标准化输出</strong>：固定的JSON格式，便于系统处理和展示</li>
                            <li>🎯 <strong>智能标注</strong>：句子级精确定位 + 四色风险标记</li>
                            <li>📖 <strong>法规解读</strong>：内置完整《广告法》全文，提供精准法条引用</li>
                            <li>🔍 <strong>多维检测</strong>：错别字标红 + 关键词高亮 + 图文一致性检测</li>
                            <li>💡 <strong>专业建议</strong>：AI+词库双重检测，提供具体可操作的修改方案</li>
                            <li>📊 <strong>可视化结果</strong>：HTML渲染 + 风险等级展示</li>
                        </ul>
                    </div>

                    <p><strong>🚀 适用场景</strong>：</p>
                    <ul>
                        <li>✅ <strong>一键分析</strong>：快速获得标准化审核报告</li>
                        <li>✅ <strong>批量处理</strong>：适合大量文案的规范化审核</li>
                        <li>✅ <strong>团队协作</strong>：统一的审核标准和输出格式</li>
                        <li>✅ <strong>系统集成</strong>：结构化数据便于后续处理</li>
                    </ul>

                    <div style="background: #ecfdf5; padding: 10px; border-radius: 6px; border-left: 4px solid #059669;">
                        <strong>💡 发展方向</strong>：智能体模式是我们重点迭代的方向，未来将推出多Agent协同审核、更精准的风险识别、行业专业化等功能。
                    </div>
                </div>

                <div class="help-section">
                    <h4>💬 对话模式</h4>
                    <p><strong>灵活的交互式咨询模式</strong>，适合个性化问题和深度交流：</p>

                    <div style="background: #fefce8; padding: 12px; border-radius: 8px; margin: 10px 0;">
                        <strong>🎯 核心特色</strong>：
                        <ul style="margin: 8px 0;">
                            <li>💬 <strong>自然对话</strong>：类似ChatGPT的自由交流体验</li>
                            <li>🔄 <strong>上下文理解</strong>：记住对话历史，支持连续提问</li>
                            <li>🎨 <strong>灵活表达</strong>：不受格式限制，回答更加生动详细</li>
                            <li>🤔 <strong>深度思考</strong>：可以进行复杂的逻辑推理和分析</li>
                        </ul>
                    </div>

                    <p><strong>🎯 适用场景</strong>：</p>
                    <ul>
                        <li>❓ <strong>法规咨询</strong>：询问具体法律条文和适用场景</li>
                        <li>✏️ <strong>文案优化</strong>：获得专业的文案改写建议</li>
                        <li>🏢 <strong>行业指导</strong>：针对特定行业的合规要求</li>
                        <li>🔍 <strong>风险评估</strong>：深度分析潜在合规风险</li>
                        <li>💡 <strong>创意讨论</strong>：探讨营销策略和创意方向</li>
                        <li>📚 <strong>学习交流</strong>：了解广告法知识和行业动态</li>
                    </ul>
                </div>

                <div class="help-section">
                    <h4>⚖️ 两种模式对比</h4>
                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                            <thead>
                                <tr style="background: #f8fafc;">
                                    <th style="border: 1px solid #e2e8f0; padding: 8px; text-align: left;">对比维度</th>
                                    <th style="border: 1px solid #e2e8f0; padding: 8px; text-align: center;">🤖 智能体模式</th>
                                    <th style="border: 1px solid #e2e8f0; padding: 8px; text-align: center;">💬 对话模式</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td style="border: 1px solid #e2e8f0; padding: 8px;"><strong>输出格式</strong></td>
                                    <td style="border: 1px solid #e2e8f0; padding: 8px;">结构化JSON，标准化</td>
                                    <td style="border: 1px solid #e2e8f0; padding: 8px;">自然语言，灵活表达</td>
                                </tr>
                                <tr>
                                    <td style="border: 1px solid #e2e8f0; padding: 8px;"><strong>审核深度</strong></td>
                                    <td style="border: 1px solid #e2e8f0; padding: 8px;">5个固定维度，全面覆盖</td>
                                    <td style="border: 1px solid #e2e8f0; padding: 8px;">根据问题灵活调整</td>
                                </tr>
                                <tr>
                                    <td style="border: 1px solid #e2e8f0; padding: 8px;"><strong>可视化效果</strong></td>
                                    <td style="border: 1px solid #e2e8f0; padding: 8px;">丰富的HTML渲染</td>
                                    <td style="border: 1px solid #e2e8f0; padding: 8px;">纯文本展示</td>
                                </tr>
                                <tr>
                                    <td style="border: 1px solid #e2e8f0; padding: 8px;"><strong>交互方式</strong></td>
                                    <td style="border: 1px solid #e2e8f0; padding: 8px;">一键分析 + 快捷操作</td>
                                    <td style="border: 1px solid #e2e8f0; padding: 8px;">自由对话 + 连续提问</td>
                                </tr>
                                <tr>
                                    <td style="border: 1px solid #e2e8f0; padding: 8px;"><strong>适用场景</strong></td>
                                    <td style="border: 1px solid #e2e8f0; padding: 8px;">标准审核、批量处理</td>
                                    <td style="border: 1px solid #e2e8f0; padding: 8px;">咨询学习、深度讨论</td>
                                </tr>
                                <tr>
                                    <td style="border: 1px solid #e2e8f0; padding: 8px;"><strong>发展重点</strong></td>
                                    <td style="border: 1px solid #e2e8f0; padding: 8px;">⭐ 主要迭代方向</td>
                                    <td style="border: 1px solid #e2e8f0; padding: 8px;">辅助功能</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="help-section">
                    <h4>💡 使用建议</h4>
                    <div style="background: #f0f9ff; padding: 12px; border-radius: 8px;">
                        <p><strong>🎯 推荐使用智能体模式</strong>：</p>
                        <ul>
                            <li>✅ 需要快速获得标准化审核结果</li>
                            <li>✅ 希望看到详细的风险标注和可视化展示</li>
                            <li>✅ 进行日常的文案合规检查</li>
                            <li>✅ 需要可复制、可分享的审核报告</li>
                        </ul>

                        <p><strong>💬 适合使用对话模式</strong>：</p>
                        <ul>
                            <li>✅ 需要深入了解某个法规条文</li>
                            <li>✅ 希望获得个性化的修改建议</li>
                            <li>✅ 想要学习广告法相关知识</li>
                            <li>✅ 需要讨论复杂的合规问题</li>
                        </ul>
                    </div>
                </div>

                <div class="help-section">
                    <h4>⚡ 快捷指令</h4>
                    <p>使用快捷按钮快速获得专业服务：</p>
                    <ul>
                        <li>🔍 <strong>强化复查</strong>：对当前文案进行更深度的审核</li>
                        <li>📖 <strong>法规索引</strong>：查询相关法律法规条文</li>
                        <li>✏️ <strong>重写文案</strong>：获得AI优化的文案版本</li>
                        <li>🏢 <strong>行业指南</strong>：获取特定行业的合规指导</li>
                    </ul>
                </div>

                <div class="help-section">
                    <h4>🎯 使用技巧</h4>
                    <ul>
                        <li>💡 <strong>分段输入</strong>：长文案建议分段审核，提高准确性</li>
                        <li>🔄 <strong>多次验证</strong>：重要内容可多次审核确保合规</li>
                        <li>📝 <strong>保存记录</strong>：重要的审核结果建议截图保存</li>
                        <li>⌨️ <strong>快捷键</strong>：Ctrl+Enter 快速发送消息</li>
                        <li>🎤 <strong>语音输入</strong>：支持语音转文字功能（开发中）</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div class="toast" id="toast"></div>

    <!-- JavaScript -->
    <!-- Markdown渲染库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js"></script>
    <script src="static/js/memory-manager.js?v=1.4"></script>
    <script src="static/js/env.js?v=1.4"></script>
    <script src="static/js/config.js?v=1.4"></script>
    <script src="static/js/api.js?v=1.4"></script>
    <script src="static/js/main.js?v=1.4"></script>
</body>
</html>
