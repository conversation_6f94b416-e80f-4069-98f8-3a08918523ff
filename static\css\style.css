/* 内容审核管家 - 主样式文件 */

/* CSS变量定义 */
:root {
    /* 主色调 - 温暖清新的蓝绿色 */
    --primary-color: #0ea5e9;
    --primary-hover: #0284c7;
    --primary-light: #e0f2fe;

    /* 辅助色 - 温暖清新的配色 */
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;

    /* 背景色 - 更温暖的白色和浅色 */
    --bg-primary: #ffffff;
    --bg-secondary: #fafbfc;
    --bg-tertiary: #f0f9ff;
    --bg-dark: #0f172a;
    --bg-dark-secondary: #1e293b;

    /* 文字色 - 更深更清晰 */
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --text-white: #ffffff;
    --text-dark: #0f172a;

    /* 边框色 - 增强对比度 */
    --border-color: #d1d5db;
    --border-hover: #9ca3af;
    --border-focus: #0ea5e9;
    --border-light: #e5e7eb;

    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* 卡片和特殊背景 */
    --bg-card: #ffffff;
    --bg-white: #ffffff;
    --bg-light: #f8fafc;
    --bg-hover: rgba(255, 255, 255, 0.8);
    --bg-secondary-hover: #e5f3ff;

    /* 高亮颜色 */
    --highlight-error-bg: #fee;
    --highlight-keyword-bg: #fff3cd;
    --highlight-safe-bg: #d4edda;

    /* 圆角 */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-full: 9999px;

    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    --font-mono: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', 'Source Code Pro', monospace;

    /* 过渡 */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;

    /* 布局 */
    --header-height: 80px;
    --sidebar-width: 280px;
    --container-max-width: 1400px;
}

/* 暗色主题变量 */
[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --text-dark: #f1f5f9;
    --border-color: #334155;
    --border-hover: #475569;
    --border-light: #475569;

    /* 卡片和特殊背景 */
    --bg-card: #1e293b;
    --bg-white: #0f172a;
    --bg-light: #334155;
    --bg-hover: rgba(51, 65, 85, 0.8);
    --bg-secondary-hover: #334155;

    /* 高亮颜色 */
    --highlight-error-bg: #3f1f1f;
    --highlight-keyword-bg: #3f3f1f;
    --highlight-safe-bg: #1f3f2f;
}

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 18px;
    line-height: 1.6;
    -webkit-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: var(--font-family);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    transition: background-color var(--transition-normal), color var(--transition-normal);
    overflow-x: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
    background: var(--border-hover);
    border-radius: var(--radius-sm);
    transition: background-color var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity var(--transition-slow), visibility var(--transition-slow);
}

.loading-overlay.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-lg);
}

.loading-text {
    color: white;
    font-size: 1.1rem;
    font-weight: 500;
    text-align: center;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 主容器 */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-md) 0;
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-lg);
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logo-icon {
    font-size: 2.5rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.logo-text {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0;
}

.logo-subtitle {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
    margin-left: var(--spacing-sm);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
    user-select: none;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-color);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
    background: var(--bg-secondary-hover);
    border-color: var(--border-hover);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn-outline {
    background: var(--bg-primary);
    color: var(--text-secondary);
    border-color: var(--border-color);
    box-shadow: var(--shadow-sm);
}

.btn-outline:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-hover);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn-large {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: 1.1rem;
    font-weight: 600;
    min-height: 52px;
    border-radius: var(--radius-lg);
}

.btn-medium {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1rem;
    font-weight: 600;
    min-height: 44px;
    border-radius: var(--radius-md);
}

.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.9rem;
    font-weight: 500;
    min-height: 36px;
}

.btn-xs {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.85rem;
    font-weight: 500;
    min-height: 28px;
}

/* 图标样式 */
.icon {
    font-size: 1em;
    line-height: 1;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 3fr 7fr;
    gap: var(--spacing-xl);
    padding: var(--spacing-xl) 0;
    min-height: calc(100vh - var(--header-height) - var(--spacing-xl) * 2);
    overflow: hidden;
    width: 100%;
    max-width: 100%;
}

/* 输入区域 */
.input-section {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
    border: 2px solid var(--border-color);
    transition: all var(--transition-normal);
    height: fit-content;
    min-width: 0;
    overflow: hidden;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

.input-section:hover {
    box-shadow: var(--shadow-xl);
    border-color: var(--border-hover);
}

/* 标签页 */
.tab-container {
    margin-bottom: var(--spacing-xl);
}

.tab-buttons {
    display: flex;
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xs);
    gap: var(--spacing-xs);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.tab-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg) var(--spacing-md);
    background: transparent;
    border: none;
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    min-height: 48px;
}

.tab-btn.active {
    background: var(--bg-primary);
    color: var(--primary-color);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--primary-color);
    font-weight: 700;
}

.tab-btn:hover:not(.active) {
    color: var(--text-primary);
    background: var(--bg-hover);
    box-shadow: var(--shadow-sm);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 输入组 */
.input-group {
    margin-bottom: var(--spacing-lg);
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    box-sizing: border-box;
}

.input-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: 1rem;
}

.text-input {
    width: 100%;
    max-width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    resize: vertical;
    min-height: 150px;
    height: 150px;
    transition: all var(--transition-fast);
    font-family: var(--font-family);
    overflow-y: auto;
    overflow-x: hidden;
    word-wrap: break-word;
    word-break: break-all;
    overflow-wrap: break-word;
    white-space: pre-wrap;
    hyphens: auto;
    box-sizing: border-box;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.text-input:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.text-input::placeholder {
    color: var(--text-muted);
    line-height: 1.6;
}

.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-sm);
    gap: var(--spacing-md);
}

.char-count {
    font-size: 0.85rem;
    color: var(--text-muted);
    font-weight: 500;
}

.input-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* 预览区域 */
.preview-section {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-lg);
    overflow: hidden;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.preview-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.preview-content {
    padding: var(--spacing-md);
    min-height: 120px;
    max-height: 300px;
    overflow-y: auto;
}

.preview-placeholder {
    color: var(--text-muted);
    font-style: italic;
    text-align: center;
    padding: var(--spacing-xl);
}

/* 上传区域 */
.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-2xl);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    margin-bottom: var(--spacing-lg);
}

.upload-area:hover {
    border-color: var(--primary-color);
    background: var(--primary-light);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background: var(--primary-light);
    transform: scale(1.02);
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
}

.upload-icon {
    font-size: 3rem;
    color: var(--text-muted);
}

.upload-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.upload-subtitle {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* 图片预览 */
.image-preview {
    margin-bottom: var(--spacing-lg);
}

.image-container {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
}

.image-container img {
    width: 100%;
    height: auto;
    max-height: 300px;
    object-fit: contain;
    display: block;
}

.remove-image {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all var(--transition-fast);
}

.remove-image:hover {
    background: var(--danger-color);
    transform: scale(1.1);
}

.image-info {
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

.image-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.image-size {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.button-group {
    display: flex;
    gap: var(--spacing-md);
}

.button-group .btn {
    flex: 1;
}

/* 结果区域 */
.result-section {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    border: 2px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
    height: fit-content;
    min-height: 600px;
    display: flex;
    flex-direction: column;
    min-width: 0;
    overflow: hidden;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

.result-section:hover {
    box-shadow: var(--shadow-xl);
    border-color: var(--border-hover);
}

/* 聊天容器 */
.chat-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 600px;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.chat-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.chat-header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.mode-switch-container {
    display: flex;
    align-items: center;
}

.mode-labels {
    display: flex;
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
    padding: var(--spacing-xs);
    gap: var(--spacing-xs);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.mode-label {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-sm);
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-secondary);
}

.mode-label small {
    color: var(--text-secondary);
}

.mode-label.active small {
    color: white;
    cursor: pointer;
    transition: all var(--transition-fast);
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mode-label.active {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--primary-hover);
}

.mode-label:hover:not(.active) {
    background: var(--bg-hover);
    color: var(--text-primary);
    box-shadow: var(--shadow-sm);
}

.chat-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
}

.status-dot.online {
    background: var(--success-color);
    animation: pulse 2s infinite;
}

.status-dot.offline {
    background: var(--danger-color);
    animation: none;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 聊天消息 */
.chat-messages {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    gap: var(--spacing-lg);
    min-height: 300px;
}

.message {
    display: flex;
    gap: var(--spacing-md);
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
    margin-bottom: var(--spacing-sm);
}

.message.fade-in {
    opacity: 1;
    transform: translateY(0);
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
    background: var(--bg-tertiary);
    border: 2px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
}

.message.user .message-avatar {
    background: linear-gradient(135deg, #0ea5e9, #0284c7, #0369a1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 2px 8px rgba(14, 165, 233, 0.25),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.message.user .message-avatar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.15) 0%,
                rgba(255, 255, 255, 0.05) 100%);
    border-radius: inherit;
    pointer-events: none;
}

.message-content {
    flex: 1;
    max-width: 85%;
}

.message.user .message-content {
    text-align: right;
}

.message-text {
    background: var(--bg-primary);
    padding: var(--spacing-lg) var(--spacing-md);
    border-radius: 1.25rem;
    color: var(--text-primary);
    line-height: 1.6;
    word-wrap: break-word;
    border: 1px solid var(--border-color);
    font-size: 0.95rem;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    position: relative;
    transition: all var(--transition-fast);
}

.message-text:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    transform: translateY(-1px);
}

.message.user .message-text {
    background: linear-gradient(135deg, #0ea5e9, #0284c7, #0369a1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 12px rgba(14, 165, 233, 0.25),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.message.user .message-text::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.1) 0%,
                rgba(255, 255, 255, 0.05) 50%,
                rgba(255, 255, 255, 0) 100%);
    border-radius: inherit;
    pointer-events: none;
}

.message.user .message-text::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #06b6d4, #0ea5e9, #3b82f6);
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.message.user .message-text:hover {
    box-shadow: 0 4px 20px rgba(14, 165, 233, 0.35),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.message.user .message-text:hover::after {
    opacity: 1;
}

/* AI助手消息特殊样式 */
.message.assistant .message-avatar {
    background: linear-gradient(135deg, #10b981, #059669, #047857);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.25),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.message.assistant .message-avatar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.15) 0%,
                rgba(255, 255, 255, 0.05) 100%);
    border-radius: inherit;
    pointer-events: none;
}

.message.assistant .message-text {
    background: linear-gradient(135deg, #f0fdf4, #ecfdf5, #d1fae5);
    border: 1px solid #d1fae5;
    border-left: 3px solid #10b981;
    box-shadow: 0 2px 12px rgba(16, 185, 129, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.5);
    position: relative;
    overflow: hidden;
}

.message.assistant .message-text::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
                rgba(16, 185, 129, 0.02) 0%,
                rgba(5, 150, 105, 0.02) 50%,
                rgba(16, 185, 129, 0.01) 100%);
    border-radius: inherit;
    opacity: 0;
    transition: opacity var(--transition-fast);
    pointer-events: none;
}

.message.assistant .message-text:hover {
    background: linear-gradient(135deg, #ecfdf5, #d1fae5, #bbf7d0);
    box-shadow: 0 4px 20px rgba(16, 185, 129, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
    transform: translateY(-1px);
}

.message.assistant .message-text:hover::before {
    opacity: 1;
}

/* 流式输出样式 */
.message.streaming .message-text::after {
    content: '▋';
    animation: blink 1s infinite;
    color: var(--primary-color);
    margin-left: 2px;
    font-weight: bold;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* 流式光标样式 */
.streaming-cursor {
    color: var(--primary-color);
    animation: blink 1s infinite;
    margin-left: 2px;
    font-weight: bold;
}

/* 流式占位符样式 */
.streaming-placeholder {
    color: var(--text-muted);
    font-style: italic;
    opacity: 0.8;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 0.4; }
}

/* 分析完成状态样式 */
.analysis-completed {
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe, #bae6fd) !important;
    border: 2px solid var(--success-color) !important;
    box-shadow: 0 4px 16px rgba(16, 185, 129, 0.2) !important;
}

.analysis-completed .analysis-header h4 {
    color: var(--success-color) !important;
    font-weight: bold;
}

.analysis-completed .analysis-timer {
    color: var(--success-color) !important;
    font-weight: bold;
}

.message.streaming .message-text {
    border-left: 3px solid var(--primary-color);
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe, #bae6fd);
    animation: streamingGlow 2s ease-in-out infinite alternate;
}

@keyframes streamingGlow {
    0% {
        box-shadow: 0 2px 12px rgba(14, 165, 233, 0.1);
    }
    100% {
        box-shadow: 0 2px 12px rgba(14, 165, 233, 0.2);
    }
}

/* 思考过程样式 */
.thinking-section {
    margin-bottom: 12px;
    border-radius: 8px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    overflow: hidden;
    transition: all 0.2s ease;
}

.thinking-header {
    padding: 8px 12px;
    background: #f8f9fa;
    color: #6c757d;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.85rem;
    user-select: none;
    transition: background 0.2s ease;
    border-bottom: 1px solid transparent;
}

.thinking-header:hover {
    background: #e9ecef;
}

.thinking-section.expanded .thinking-header {
    border-bottom: 1px solid #e9ecef;
}

.thinking-title {
    color: #6c757d;
    font-size: 0.85rem;
    font-weight: normal;
    display: flex;
    align-items: center;
    gap: 6px;
}

.thinking-time {
    font-size: 0.75rem;
    color: #9ca3af;
    background: #e5e7eb;
    padding: 2px 6px;
    border-radius: 10px;
    font-family: monospace;
    font-weight: 500;
    margin-left: auto;
    margin-right: 8px;
}

.thinking-toggle {
    font-size: 0.75rem;
    color: #6c757d;
    transition: transform 0.2s ease;
    font-family: monospace;
}

.thinking-section.expanded .thinking-toggle {
    transform: rotate(180deg);
}

.thinking-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
    background: #ffffff;
    font-size: 0.85rem;
    line-height: 1.6;
    color: #495057;
}

.thinking-section.expanded .thinking-content {
    max-height: 600px;
    padding: 12px 16px;
}

.thinking-content p {
    margin: 8px 0;
}

.thinking-content pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px;
    margin: 8px 0;
    font-size: 0.8rem;
    overflow-x: auto;
}

.thinking-content code {
    background: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.8rem;
}

/* 暗色主题下的思考过程样式 - 增强优先级 */
html[data-theme="dark"] .thinking-section,
[data-theme="dark"] .thinking-section {
    background: #1e293b !important;
    border: 2px solid #475569 !important;
    border-left: 4px solid #8b5cf6 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

html[data-theme="dark"] .thinking-header,
[data-theme="dark"] .thinking-header {
    background: #1e293b !important;
    color: #cbd5e1 !important;
}

html[data-theme="dark"] .thinking-header:hover,
[data-theme="dark"] .thinking-header:hover {
    background: #334155 !important;
}

html[data-theme="dark"] .thinking-title,
[data-theme="dark"] .thinking-title {
    color: #cbd5e1 !important;
}

html[data-theme="dark"] .thinking-time,
[data-theme="dark"] .thinking-time {
    color: #94a3b8 !important;
    background: #334155 !important;
}

html[data-theme="dark"] .thinking-toggle,
[data-theme="dark"] .thinking-toggle {
    color: #cbd5e1 !important;
}

html[data-theme="dark"] .thinking-content,
[data-theme="dark"] .thinking-content {
    background: #0f172a !important;
    color: #f1f5f9 !important;
}

html[data-theme="dark"] .thinking-section.expanded .thinking-header,
[data-theme="dark"] .thinking-section.expanded .thinking-header {
    border-bottom: 2px solid #475569 !important;
}

html[data-theme="dark"] .thinking-content pre,
[data-theme="dark"] .thinking-content pre {
    background: #1e293b !important;
    border: 1px solid #475569 !important;
    color: #f1f5f9 !important;
}

html[data-theme="dark"] .thinking-content code,
[data-theme="dark"] .thinking-content code {
    background: #334155 !important;
    color: #f1f5f9 !important;
}

html[data-theme="dark"] .thinking-text,
[data-theme="dark"] .thinking-text {
    color: #f1f5f9 !important;
}

/* 深度思考控制按钮样式 */
.thinking-toggle-btn {
    transition: all 0.3s ease;
    border-color: #e5e7eb;
    background: #f9fafb;
}

.thinking-toggle-btn:hover {
    border-color: #d1d5db;
    background: #f3f4f6;
}

.thinking-toggle-btn.active {
    border-color: #8b5cf6;
    background: #f3e8ff;
    color: #7c3aed;
}

.thinking-toggle-btn.active .icon {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* 暗色主题下的深度思考按钮 */
[data-theme="dark"] .thinking-toggle-btn {
    border-color: #4a5568;
    background: #2d3748;
    color: #e2e8f0;
}

[data-theme="dark"] .thinking-toggle-btn:hover {
    border-color: #718096;
    background: #4a5568;
}

[data-theme="dark"] .thinking-toggle-btn.active {
    border-color: #9f7aea;
    background: #553c9a;
    color: #d6bcfa;
}

/* 暗色主题下的消息样式 - 使用更强的选择器 */
html[data-theme="dark"] .message.assistant .message-text,
[data-theme="dark"] .message.assistant .message-text {
    background: linear-gradient(135deg, #1f2937, #374151, #4b5563) !important;
    border: 1px solid #4b5563 !important;
    border-left: 3px solid #10b981 !important;
    box-shadow: 0 2px 12px rgba(16, 185, 129, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    color: #f1f5f9 !important;
}

html[data-theme="dark"] .message.assistant .message-text:hover,
[data-theme="dark"] .message.assistant .message-text:hover {
    background: linear-gradient(135deg, #374151, #4b5563, #6b7280) !important;
    box-shadow: 0 4px 20px rgba(16, 185, 129, 0.25),
                inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
    transform: translateY(-1px);
    color: #f1f5f9 !important;
}

html[data-theme="dark"] .message.streaming .message-text,
[data-theme="dark"] .message.streaming .message-text {
    border-left: 3px solid var(--primary-color) !important;
    background: linear-gradient(135deg, #1e293b, #334155, #475569) !important;
    animation: streamingGlow 2s ease-in-out infinite alternate;
    color: #f1f5f9 !important;
}

[data-theme="dark"] .analysis-completed {
    background: linear-gradient(135deg, #1e293b, #334155, #475569) !important;
    border: 2px solid var(--success-color) !important;
    box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3) !important;
}

/* 暗色主题下的消息内容文字颜色 */
[data-theme="dark"] .message.assistant .message-text * {
    color: #f1f5f9 !important;
}

[data-theme="dark"] .message.assistant .message-text p,
[data-theme="dark"] .message.assistant .message-text div,
[data-theme="dark"] .message.assistant .message-text span,
[data-theme="dark"] .message.assistant .message-text h1,
[data-theme="dark"] .message.assistant .message-text h2,
[data-theme="dark"] .message.assistant .message-text h3,
[data-theme="dark"] .message.assistant .message-text h4,
[data-theme="dark"] .message.assistant .message-text h5,
[data-theme="dark"] .message.assistant .message-text h6,
[data-theme="dark"] .message.assistant .message-text strong,
[data-theme="dark"] .message.assistant .message-text em,
[data-theme="dark"] .message.assistant .message-text li,
[data-theme="dark"] .message.assistant .message-text ul,
[data-theme="dark"] .message.assistant .message-text ol,
[data-theme="dark"] .message.assistant .message-text code,
[data-theme="dark"] .message.assistant .message-text pre,
[data-theme="dark"] .message.assistant .message-text a,
[data-theme="dark"] .message.assistant .message-text blockquote {
    color: #f1f5f9 !important;
}

/* 暗色主题下的链接颜色 */
[data-theme="dark"] .message.assistant .message-text a {
    color: #60a5fa !important;
}

[data-theme="dark"] .message.assistant .message-text a:hover {
    color: #93c5fd !important;
}

/* 暗色主题下的代码块和表格 */
[data-theme="dark"] .message.assistant .message-text pre {
    background: #1f2937 !important;
    border: 1px solid #374151 !important;
    color: #f1f5f9 !important;
}

[data-theme="dark"] .message.assistant .message-text code {
    background: #374151 !important;
    color: #f1f5f9 !important;
}

[data-theme="dark"] .message.assistant .message-text table {
    border-color: #374151 !important;
}

[data-theme="dark"] .message.assistant .message-text th,
[data-theme="dark"] .message.assistant .message-text td {
    border-color: #374151 !important;
    color: #f1f5f9 !important;
}

[data-theme="dark"] .message.assistant .message-text th {
    background: #374151 !important;
}

[data-theme="dark"] .message.assistant .message-text blockquote {
    border-left-color: #60a5fa !important;
    background: rgba(96, 165, 250, 0.1) !important;
}

/* 暗色主题下的消息伪元素 */
[data-theme="dark"] .message.assistant .message-text::before {
    background: transparent !important;
}

[data-theme="dark"] .message.assistant .message-text::after {
    background: transparent !important;
}

/* 暗色主题下的消息气泡样式 */
html[data-theme="dark"] .message-bubble.assistant-bubble,
[data-theme="dark"] .message-bubble.assistant-bubble {
    background: linear-gradient(135deg, #1f2937, #374151, #4b5563) !important;
    border: 1px solid #4b5563 !important;
    color: #f1f5f9 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

html[data-theme="dark"] .answer-text,
[data-theme="dark"] .answer-text {
    color: #f1f5f9 !important;
}

/* 暗色主题下的基础消息样式 */
[data-theme="dark"] .message-text {
    background: var(--bg-primary) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
}

/* 暗色主题下的输入区域和结果区域 */
[data-theme="dark"] .input-section {
    background: var(--bg-primary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .result-section {
    background: var(--bg-primary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .chat-container {
    background: var(--bg-primary) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .chat-input-container {
    background: var(--bg-primary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .chat-input {
    background: var(--bg-primary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .chat-input:focus {
    border-color: var(--border-focus) !important;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.2) !important;
}

[data-theme="dark"] .chat-input::placeholder {
    color: var(--text-muted) !important;
}

/* 暗色主题下的文本输入框 */
[data-theme="dark"] .text-input {
    background: var(--bg-primary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
    box-shadow: inset 0 1px 3px rgba(255, 255, 255, 0.05) !important;
}

[data-theme="dark"] .text-input:focus {
    border-color: var(--border-focus) !important;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.2) !important;
}

[data-theme="dark"] .text-input::placeholder {
    color: var(--text-muted) !important;
}

/* 暗色主题下的聊天头部 */
[data-theme="dark"] .chat-header {
    background: var(--bg-secondary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .chat-title {
    color: var(--text-primary) !important;
}

/* 暗色主题下的预览区域 */
[data-theme="dark"] .preview-section {
    background: var(--bg-secondary) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .preview-header {
    background: var(--bg-tertiary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .preview-content {
    background: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .preview-placeholder {
    color: var(--text-muted) !important;
}

/* 暗色主题下的上传区域 */
[data-theme="dark"] .upload-area {
    border-color: var(--border-color) !important;
    background: var(--bg-primary) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .upload-area:hover,
[data-theme="dark"] .upload-area.dragover {
    border-color: var(--primary-color) !important;
    background: rgba(14, 165, 233, 0.1) !important;
}

[data-theme="dark"] .upload-icon {
    color: var(--text-muted) !important;
}

[data-theme="dark"] .upload-title {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .upload-subtitle {
    color: var(--text-secondary) !important;
}

/* 暗色主题下的图片预览 */
[data-theme="dark"] .image-container {
    background: var(--bg-tertiary) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .image-info {
    background: var(--bg-secondary) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .image-name {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .image-size {
    color: var(--text-secondary) !important;
}

/* 主要内容样式 */
.main-content {
    margin-top: 8px;
}

.message-time {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
    font-weight: 500;
}

/* 快捷操作 */
.quick-actions {
    padding: var(--spacing-sm) var(--spacing-md);
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.quick-actions-header {
    font-size: 0.7rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.quick-buttons {
    display: flex;
    gap: var(--spacing-xs);
    overflow-x: auto;
    padding-bottom: var(--spacing-xs);
    scrollbar-width: thin;
}

.quick-buttons::-webkit-scrollbar {
    height: 4px;
}

.quick-buttons::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
}

.quick-buttons::-webkit-scrollbar-thumb {
    background: var(--border-hover);
    border-radius: var(--radius-sm);
}

.quick-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-size: 0.8rem;
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    min-height: 32px;
    white-space: nowrap;
    flex-shrink: 0;
    box-shadow: var(--shadow-sm);
}

.quick-btn:hover {
    background: var(--primary-light);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.quick-btn .icon {
    font-size: 0.9rem;
}

.quick-btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

/* 快捷按钮加载状态 */
.quick-btn.loading {
    opacity: 0.7;
    cursor: not-allowed;
    pointer-events: none;
}

.quick-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 12px;
    height: 12px;
    margin: -6px 0 0 -6px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 聊天输入 */
.chat-input-container {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    background: var(--bg-primary);
    border-radius: 0 0 var(--radius-xl) var(--radius-xl);
}

.chat-input-wrapper {
    display: flex;
    gap: var(--spacing-md);
    align-items: flex-end;
}

.chat-input-group {
    flex: 1;
    position: relative;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    box-sizing: border-box;
}

.chat-input {
    width: 100%;
    max-width: 100%;
    min-height: 120px;
    max-height: 200px;
    padding: var(--spacing-md);
    padding-right: 140px;
    padding-bottom: 50px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    resize: none;
    transition: all var(--transition-fast);
    font-family: var(--font-family);
    overflow-x: hidden;
    overflow-y: auto;
    word-wrap: break-word;
    word-break: break-all;
    overflow-wrap: break-word;
    white-space: pre-wrap;
    hyphens: auto;
    box-sizing: border-box;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chat-input:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.chat-input::placeholder {
    color: var(--text-muted);
    line-height: 1.5;
}

.chat-input-actions {
    position: absolute;
    right: var(--spacing-sm);
    bottom: var(--spacing-sm);
    display: flex;
    gap: var(--spacing-xs);
}

.chat-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm);
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    min-width: 80px;
    height: 32px;
}

.chat-action-btn:hover {
    background: var(--primary-light);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chat-action-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.chat-action-btn.recording {
    background: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
    animation: pulse-recording 1.5s infinite;
}

@keyframes pulse-recording {
    0% {
        opacity: 1;
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }
    70% {
        opacity: 0.8;
        box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    }
    100% {
        opacity: 1;
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    }
}

.send-btn {
    padding: var(--spacing-md) var(--spacing-xl);
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    min-width: 100px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.send-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
}

.help-section {
    margin-bottom: var(--spacing-lg);
}

.help-section h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.help-section p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.help-section ul {
    margin: var(--spacing-sm) 0;
    padding-left: var(--spacing-lg);
}

.help-section li {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-xs);
}

/* 功能排期样式 */
.roadmap-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    transition: all var(--transition-fast);
}

.roadmap-item:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--border-hover);
}

.roadmap-status {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.roadmap-status.developing {
    background: var(--warning-color);
    color: white;
}

.roadmap-status.planning {
    background: var(--info-color);
    color: white;
}

.roadmap-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.roadmap-desc {
    color: var(--text-secondary);
    line-height: 1.5;
    margin-bottom: var(--spacing-xs);
}

.roadmap-eta {
    font-size: 0.85rem;
    color: var(--text-muted);
    font-style: italic;
}

/* 提示框 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--bg-primary);
    color: var(--text-primary);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    z-index: 1001;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
    max-width: 400px;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    border-color: var(--success-color);
    background: var(--success-color);
    color: white;
}

.toast.error {
    border-color: var(--danger-color);
    background: var(--danger-color);
    color: white;
}

.toast.warning {
    border-color: var(--warning-color);
    background: var(--warning-color);
    color: white;
}

/* 大屏幕优化 */
@media (min-width: 1200px) {
    .quick-buttons {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: var(--spacing-xs);
        overflow-x: visible;
    }
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .quick-buttons {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-xs);
        overflow-x: visible;
    }

    .quick-btn {
        min-height: 36px;
        font-size: 0.75rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    .header-content {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    /* 移动端按钮调整 */
    .btn-large {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: 0.9rem;
        min-height: 48px;
    }

    .btn-medium {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.85rem;
        min-height: 40px;
    }

    .tab-btn {
        padding: var(--spacing-md) var(--spacing-sm);
        font-size: 0.8rem;
        min-height: 44px;
    }

    .quick-btn {
        padding: var(--spacing-sm);
        font-size: 0.75rem;
        min-height: 36px;
    }

    .send-btn {
        min-width: 80px;
        height: 44px;
        font-size: 0.85rem;
    }

    .mode-label {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.75rem;
        min-height: 28px;
    }

    .logo {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .logo-subtitle {
        margin-left: 0;
    }

    .input-section,
    .result-section {
        padding: var(--spacing-lg);
    }

    .tab-buttons {
        flex-direction: column;
    }

    .input-footer {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-sm);
    }

    .input-actions {
        justify-content: center;
    }

    .button-group {
        flex-direction: column;
    }

    .chat-input-wrapper {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .chat-input {
        padding-right: var(--spacing-md);
    }

    .chat-input-actions {
        position: static;
        transform: none;
        justify-content: center;
        margin-top: var(--spacing-sm);
    }
}

/* 图片分析卡片样式 */
.image-analysis-card .card-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.image-analysis-card h5 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.image-analysis-card .image-description,
.image-analysis-card .extracted-text,
.image-analysis-card .structured-info,
.image-analysis-card .consistency-check {
    padding: var(--spacing-md);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.image-analysis-card .image-description p,
.image-analysis-card .extracted-text p,
.image-analysis-card .structured-info p {
    margin: 0;
    line-height: 1.6;
    color: var(--text-secondary);
}

.image-analysis-card .consistency-check p {
    margin: var(--spacing-xs) 0 0 0;
    line-height: 1.5;
    color: var(--text-secondary);
}

.image-analysis-card .consistency-check p:first-child {
    margin-top: 0;
}

.image-analysis-card .consistency-check strong {
    color: var(--text-primary);
    font-weight: 600;
}

/* 分析进度动画 - 紧凑现代设计 */
.analysis-progress {
    display: flex !important;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    padding: 20px 18px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    border: 1px solid rgba(148, 163, 184, 0.15);
    margin: var(--spacing-md) 0;
    box-shadow:
        0 8px 20px rgba(0, 0, 0, 0.06),
        0 2px 6px rgba(0, 0, 0, 0.03);
    position: relative;
    overflow: hidden;
    visibility: visible !important;
    opacity: 1 !important;
    max-width: 380px;
    margin-left: auto;
    margin-right: auto;
}

.analysis-progress::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #3b82f6, transparent);
    opacity: 0.6;
}

.analysis-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.analysis-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 分析头部样式 - 紧凑设计 */
.analysis-header {
    display: flex !important;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 18px;
    text-align: left;
}

.analysis-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

/* 紧凑的进度指示器 */
.progress-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: conic-gradient(from 0deg, #3b82f6 120deg, #e2e8f0 120deg);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    animation: rotate 1.5s linear infinite;
    flex-shrink: 0;
    margin: 0 8px;
}

.progress-circle::before {
    content: '';
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #ffffff;
    position: absolute;
}

.progress-circle::after {
    content: '⚡';
    font-size: 14px;
    z-index: 1;
    position: relative;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.analysis-steps {
    display: flex !important;
    flex-direction: column;
    gap: 8px;
    width: 100%;
}

.analysis-step {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 14px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    border: 1px solid rgba(148, 163, 184, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
    position: relative;
    overflow: hidden;
}

.analysis-step::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: #e2e8f0;
    transition: all 0.3s ease;
}

.analysis-step.active {
    background: linear-gradient(135deg, #dbeafe 0%, #eff6ff 100%);
    border-color: #3b82f6;
    transform: translateX(4px);
    box-shadow: 0 3px 10px rgba(59, 130, 246, 0.12);
}

.analysis-step.active::before {
    background: #3b82f6;
    width: 4px;
}

.analysis-step.completed {
    background: linear-gradient(135deg, #dcfce7 0%, #f0fdf4 100%);
    border-color: #22c55e;
    box-shadow: 0 2px 6px rgba(34, 197, 94, 0.08);
}

.analysis-step.completed::before {
    background: #22c55e;
    width: 4px;
}

.step-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #e2e8f0;
    color: #64748b;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    flex-shrink: 0;
    transition: all 0.3s ease;
    position: relative;
}

.step-icon.pending {
    background: #e2e8f0;
    color: #64748b;
}

.step-icon.active {
    background: #3b82f6;
    color: white;
    animation: pulse-compact 1.5s infinite;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.step-icon.completed {
    background: #22c55e;
    color: white;
}

.step-icon.completed::after {
    content: '✓';
    position: absolute;
    font-size: 14px;
}

@keyframes pulse-compact {
    0%, 100% {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
    }
    50% {
        box-shadow: 0 0 0 6px rgba(59, 130, 246, 0.1);
    }
}

.step-text {
    font-size: 14px;
    color: #334155;
    font-weight: 500;
    line-height: 1.3;
    flex: 1;
}

.analysis-step.active .step-text {
    color: #1e40af;
    font-weight: 600;
}

.analysis-step.completed .step-text {
    color: #166534;
}

.analysis-timer {
    font-size: 12px;
    color: #64748b;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    background: rgba(255, 255, 255, 0.9);
    padding: 4px 10px;
    border-radius: 12px;
    border: 1px solid rgba(148, 163, 184, 0.15);
    backdrop-filter: blur(8px);
    font-weight: 500;
    letter-spacing: 0.3px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
    flex-shrink: 0;
}

/* 语音输入动画 */
.voice-loading {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.loading-dots {
    display: flex;
    gap: 2px;
}

.loading-dots span {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: currentColor;
    animation: loadingDots 1.4s infinite ease-in-out;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes loadingDots {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

/* 风险等级样式 */
.risk-high { color: var(--danger-color); font-weight: 600; }
.risk-medium { color: var(--warning-color); font-weight: 600; }
.risk-low { color: var(--info-color); font-weight: 600; }
.risk-safe { color: var(--success-color); font-weight: 600; }

/* 高亮样式 */
.highlight-error { background-color: var(--highlight-error-bg); color: var(--danger-color); padding: 2px 4px; border-radius: 3px; }
.highlight-keyword { background-color: var(--highlight-keyword-bg); color: var(--warning-color); padding: 2px 4px; border-radius: 3px; }
.highlight-safe { background-color: var(--highlight-safe-bg); color: var(--success-color); padding: 2px 4px; border-radius: 3px; }

/* 审核结果样式 */
.audit-result {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin: var(--spacing-md) 0;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.risk-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    font-size: 1.1rem;
}

.risk-score {
    background: var(--bg-tertiary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-weight: 600;
    color: var(--text-primary);
}

.result-summary {
    margin-bottom: var(--spacing-lg);
}

.result-summary h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.result-summary p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.result-issues {
    margin-bottom: var(--spacing-lg);
}

.result-issues h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.issues-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.issue-item {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
}

.issue-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
}

.issue-risk {
    font-size: 1.2rem;
}

.issue-text {
    background: var(--bg-tertiary);
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-family: var(--font-mono);
    font-size: 0.875rem;
}

.issue-reason {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    font-size: 0.875rem;
}

.issue-suggestion {
    color: var(--text-primary);
    background: var(--primary-light);
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    border-left: 3px solid var(--primary-color);
}

.result-suggestions {
    margin-bottom: var(--spacing-lg);
}

.result-suggestions h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.result-suggestions ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.result-suggestions li {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
    line-height: 1.6;
}

.result-suggestions li:before {
    content: "💡";
    margin-right: var(--spacing-sm);
}

/* 切片预览样式 */
.chunks-preview {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.chunk-item {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    overflow: hidden;
}

.chunk-header {
    background: var(--bg-tertiary);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-secondary);
    border-bottom: 1px solid var(--border-color);
}

.chunk-content {
    padding: var(--spacing-md);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 0.875rem;
}

/* 重写内容样式 */
.rewritten-content {
    margin: var(--spacing-md) 0;
}

.rewritten-content h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.content-box {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    color: var(--text-primary);
    line-height: 1.6;
    border-left: 3px solid var(--success-color);
}

/* 代码样式 */
code {
    background: var(--bg-tertiary);
    color: var(--primary-color);
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-family: var(--font-mono);
    font-size: 0.875rem;
}

/* 链接样式 */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

/* ==================== 智能体模式卡片渲染样式 ==================== */

/* 智能切片可视化组件样式 */
.message.intelligent-segments {
    margin-bottom: 20px;
}

.intelligent-analysis-header {
    margin-bottom: 15px;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: var(--radius-lg);
    color: white;
    text-align: center;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.intelligent-analysis-header h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
    font-weight: 600;
}

.intelligent-analysis-header p {
    margin: 0;
    font-size: 12px;
    opacity: 0.9;
}

/* 风险统计样式 */
.risk-statistics {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin-bottom: 15px;
    padding: 15px;
    background: var(--bg-light);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
}

.stat-item {
    text-align: center;
    padding: 10px;
    background: var(--bg-white);
    border-radius: var(--radius-md);
    transition: transform 0.2s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
}

.stat-emoji {
    font-size: 20px;
    margin-bottom: 6px;
}

.stat-label {
    font-size: 11px;
    color: var(--text-muted);
    margin-bottom: 3px;
    font-weight: 500;
}

.stat-count {
    font-size: 18px;
    font-weight: bold;
    color: var(--text-dark);
}

/* 智能切片容器 */
.intelligent-segments-container {
    margin-top: 15px;
}

/* 单个智能切片样式 */
.intelligent-segment {
    margin: 10px 0;
    padding: 15px;
    border-radius: var(--radius-lg);
    border-left: 4px solid;
    transition: all 0.3s ease;
    position: relative;
    background: var(--bg-white);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.intelligent-segment:hover {
    transform: translateX(6px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* 风险等级样式 */
.intelligent-segment.urgent {
    border-left-color: #ff4757;
    background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%);
}

.intelligent-segment.warning {
    border-left-color: #ffa502;
    background: linear-gradient(135deg, #fffbf0 0%, #fff3e0 100%);
}

.intelligent-segment.notice {
    border-left-color: #9c88ff;
    background: linear-gradient(135deg, #f8f7ff 0%, #f0edff 100%);
}

.intelligent-segment.safe {
    border-left-color: #2ed573;
    background: linear-gradient(135deg, #f0fff4 0%, #e8f5e8 100%);
}

.segment-content {
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 10px;
    color: var(--text-dark);
    font-weight: 500;
}

.segment-risk {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 8px;
}

.segment-risk.urgent { color: #ff4757; }
.segment-risk.warning { color: #ffa502; }
.segment-risk.notice { color: #9c88ff; }
.segment-risk.safe { color: #2ed573; }

.segment-reason {
    font-size: 12px;
    color: var(--text-muted);
    background: rgba(0, 0, 0, 0.03);
    padding: 8px 12px;
    border-radius: var(--radius-md);
    border-left: 3px solid var(--border-light);
    margin: 8px 0;
}

.segment-suggestion {
    font-size: 12px;
    color: #0066cc;
    background: rgba(0, 102, 204, 0.05);
    padding: 8px 12px;
    border-radius: var(--radius-md);
    margin-top: 8px;
}

/* 分析卡片样式 */
.message.analysis-cards {
    margin-bottom: 20px;
}

.analysis-cards-header {
    margin-bottom: 20px;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: var(--radius-lg);
    color: white;
    text-align: center;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.analysis-cards-header h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
    font-weight: 600;
}

.analysis-cards-header p {
    margin: 0;
    font-size: 12px;
    opacity: 0.9;
}

.analysis-card {
    margin-bottom: 15px;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.analysis-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 20px;
    font-weight: 600;
    color: white;
}

.card-icon {
    font-size: 20px;
}

.card-title {
    font-size: 14px;
}

.card-content {
    padding: 20px;
    background: var(--bg-white);
}

/* 整体评估卡片 */
.overall-assessment.urgent .card-header {
    background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
}

.overall-assessment.warning .card-header {
    background: linear-gradient(135deg, #ffa502 0%, #ff9500 100%);
}

.overall-assessment.notice .card-header {
    background: linear-gradient(135deg, #9c88ff 0%, #8c7ae6 100%);
}

.overall-assessment.safe .card-header {
    background: linear-gradient(135deg, #2ed573 0%, #26d0ce 100%);
}

.risk-level {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
}

.risk-summary {
    font-size: 14px;
    color: var(--text-muted);
    line-height: 1.5;
}

/* 统计卡片 */
.statistics-card .card-header {
    background: linear-gradient(135deg, #3742fa 0%, #2f3542 100%);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.stat-description {
    font-size: 12px;
    color: var(--text-muted);
}

/* 问题列表卡片 */
.issues-card .card-header {
    background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
}

.issues-card .issue-item {
    padding: 15px;
    margin-bottom: 10px;
    background: var(--bg-light);
    border-radius: var(--radius-md);
    border-left: 4px solid #ff4757;
}

.issues-card .issue-item:last-child {
    margin-bottom: 0;
}

.issues-card .issue-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-weight: 600;
}

.issues-card .issue-text {
    color: var(--text-dark);
}

.issues-card .issue-description {
    font-size: 13px;
    color: var(--text-muted);
    margin-bottom: 8px;
}

.issues-card .issue-suggestion {
    font-size: 13px;
    color: #0066cc;
    background: rgba(0, 102, 204, 0.05);
    padding: 8px 12px;
    border-radius: var(--radius-sm);
}

/* 关键词卡片 */
.keywords-card .card-header {
    background: linear-gradient(135deg, #2ed573 0%, #26d0ce 100%);
}

.keyword-item {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 8px 12px;
    margin: 5px;
    background: var(--bg-light);
    border-radius: var(--radius-full);
    font-size: 13px;
    font-weight: 500;
}

.keyword-type {
    font-size: 11px;
    color: var(--text-muted);
}

/* 消息动画效果 */
.message.fade-in {
    opacity: 1;
    transform: translateY(0);
}

/* 深色主题适配 */
[data-theme="dark"] .analysis-cards-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

[data-theme="dark"] .analysis-card {
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .card-content {
    background: var(--bg-card);
}

[data-theme="dark"] .issues-card .issue-item {
    background: var(--bg-secondary);
}

[data-theme="dark"] .keyword-item {
    background: var(--bg-secondary);
}

/* 暗色主题下的分析进度组件 */
[data-theme="dark"] .analysis-progress {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border: 1px solid rgba(71, 85, 105, 0.3);
    box-shadow:
        0 8px 20px rgba(0, 0, 0, 0.2),
        0 2px 6px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .analysis-progress::before {
    background: linear-gradient(90deg, transparent, #0ea5e9, transparent);
}

[data-theme="dark"] .analysis-header h4 {
    color: #f1f5f9;
}

[data-theme="dark"] .progress-circle::before {
    background: #1e293b;
}

[data-theme="dark"] .analysis-step {
    background: rgba(30, 41, 59, 0.9);
    border: 1px solid rgba(71, 85, 105, 0.2);
}

[data-theme="dark"] .analysis-step.active {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
    border-color: #3b82f6;
}

[data-theme="dark"] .analysis-step.completed {
    background: linear-gradient(135deg, #14532d 0%, #166534 100%);
    border-color: #22c55e;
}

[data-theme="dark"] .step-text {
    color: #cbd5e1;
}

[data-theme="dark"] .analysis-step.active .step-text {
    color: #dbeafe;
}

[data-theme="dark"] .analysis-step.completed .step-text {
    color: #dcfce7;
}

[data-theme="dark"] .analysis-timer {
    background: rgba(30, 41, 59, 0.9);
    border: 1px solid rgba(71, 85, 105, 0.3);
    color: #94a3b8;
}

/* 暗色主题下的智能分析组件 */
[data-theme="dark"] .intelligent-segment {
    background: var(--bg-card);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .intelligent-segment:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .intelligent-segment.urgent {
    background: linear-gradient(135deg, #3f1f1f 0%, #4a1f1f 100%);
}

[data-theme="dark"] .intelligent-segment.warning {
    background: linear-gradient(135deg, #3f3f1f 0%, #4a4a1f 100%);
}

[data-theme="dark"] .intelligent-segment.notice {
    background: linear-gradient(135deg, #2f2f3f 0%, #3a3a4a 100%);
}

[data-theme="dark"] .intelligent-segment.safe {
    background: linear-gradient(135deg, #1f3f2f 0%, #1f4a2f 100%);
}

[data-theme="dark"] .segment-suggestion {
    color: #60a5fa;
    background: rgba(96, 165, 250, 0.1);
}

[data-theme="dark"] .issues-card .issue-suggestion {
    color: #60a5fa;
    background: rgba(96, 165, 250, 0.1);
}

/* 响应式设计 - 卡片样式 */
@media (max-width: 768px) {
    .risk-statistics {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .intelligent-segment {
        padding: 12px;
    }

    .analysis-card {
        margin-bottom: 12px;
    }

    .card-header {
        padding: 12px 15px;
    }

    .card-content {
        padding: 15px;
    }

    .analysis-cards-header {
        padding: 12px;
        margin-bottom: 15px;
    }

    .analysis-cards-header h4 {
        font-size: 16px;
    }

    .analysis-cards-header p {
        font-size: 12px;
    }

    .stat-item {
        padding: 12px;
    }

    .stat-value {
        font-size: 18px;
    }

    .issues-card .issue-item,
    .keyword-item {
        padding: 12px;
    }
}
