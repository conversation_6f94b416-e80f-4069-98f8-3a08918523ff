/**
 * 内存管理器测试脚本
 * 用于验证内存管理功能是否正常工作
 */

class MemoryTester {
    constructor() {
        this.testResults = [];
        this.isRunning = false;
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        if (this.isRunning) {
            console.log('测试已在运行中...');
            return;
        }

        this.isRunning = true;
        this.testResults = [];
        
        console.log('🧪 开始内存管理器测试...');
        
        try {
            await this.testMemoryManagerExists();
            await this.testTimerManagement();
            await this.testIntervalManagement();
            await this.testMemoryCleanup();
            await this.testLocalStorageCleanup();
            await this.testMemoryMonitoring();
            
            this.showTestResults();
        } catch (error) {
            console.error('❌ 测试过程中出现错误:', error);
        } finally {
            this.isRunning = false;
        }
    }

    /**
     * 测试内存管理器是否存在
     */
    async testMemoryManagerExists() {
        const testName = '内存管理器实例检查';
        try {
            if (window.memoryManager) {
                this.addTestResult(testName, true, '内存管理器实例存在');
            } else {
                this.addTestResult(testName, false, '内存管理器实例不存在');
            }
        } catch (error) {
            this.addTestResult(testName, false, `错误: ${error.message}`);
        }
    }

    /**
     * 测试定时器管理
     */
    async testTimerManagement() {
        const testName = '定时器管理测试';
        try {
            if (!window.memoryManager) {
                this.addTestResult(testName, false, '内存管理器不存在');
                return;
            }

            const initialTimerCount = window.memoryManager.timers.size;
            
            // 创建测试定时器
            const timerId = window.memoryManager.registerTimer(
                setTimeout(() => {}, 1000)
            );
            
            const afterCreateCount = window.memoryManager.timers.size;
            
            // 清理定时器
            window.memoryManager.clearTimer(timerId);
            
            const afterClearCount = window.memoryManager.timers.size;
            
            if (afterCreateCount === initialTimerCount + 1 && 
                afterClearCount === initialTimerCount) {
                this.addTestResult(testName, true, '定时器注册和清理正常');
            } else {
                this.addTestResult(testName, false, 
                    `定时器计数异常: 初始${initialTimerCount}, 创建后${afterCreateCount}, 清理后${afterClearCount}`);
            }
        } catch (error) {
            this.addTestResult(testName, false, `错误: ${error.message}`);
        }
    }

    /**
     * 测试间隔器管理
     */
    async testIntervalManagement() {
        const testName = '间隔器管理测试';
        try {
            if (!window.memoryManager) {
                this.addTestResult(testName, false, '内存管理器不存在');
                return;
            }

            const initialIntervalCount = window.memoryManager.intervals.size;
            
            // 创建测试间隔器
            const intervalId = window.memoryManager.registerInterval(
                setInterval(() => {}, 1000)
            );
            
            const afterCreateCount = window.memoryManager.intervals.size;
            
            // 清理间隔器
            window.memoryManager.clearInterval(intervalId);
            
            const afterClearCount = window.memoryManager.intervals.size;
            
            if (afterCreateCount === initialIntervalCount + 1 && 
                afterClearCount === initialIntervalCount) {
                this.addTestResult(testName, true, '间隔器注册和清理正常');
            } else {
                this.addTestResult(testName, false, 
                    `间隔器计数异常: 初始${initialIntervalCount}, 创建后${afterCreateCount}, 清理后${afterClearCount}`);
            }
        } catch (error) {
            this.addTestResult(testName, false, `错误: ${error.message}`);
        }
    }

    /**
     * 测试内存清理功能
     */
    async testMemoryCleanup() {
        const testName = '内存清理功能测试';
        try {
            if (!window.memoryManager) {
                this.addTestResult(testName, false, '内存管理器不存在');
                return;
            }

            // 创建一些测试消息
            const chatMessages = document.getElementById('chat-messages');
            if (chatMessages) {
                const initialMessageCount = chatMessages.children.length;
                
                // 添加测试消息
                for (let i = 0; i < 10; i++) {
                    const testMessage = document.createElement('div');
                    testMessage.className = 'message test-message';
                    testMessage.textContent = `测试消息 ${i}`;
                    chatMessages.appendChild(testMessage);
                }
                
                const afterAddCount = chatMessages.children.length;
                
                // 执行清理
                window.memoryManager.cleanupOldMessages();
                
                const afterCleanupCount = chatMessages.children.length;
                
                // 清理测试消息
                const testMessages = chatMessages.querySelectorAll('.test-message');
                testMessages.forEach(msg => msg.remove());
                
                this.addTestResult(testName, true, 
                    `消息清理测试完成: 初始${initialMessageCount}, 添加后${afterAddCount}, 清理后${afterCleanupCount}`);
            } else {
                this.addTestResult(testName, false, '找不到聊天消息容器');
            }
        } catch (error) {
            this.addTestResult(testName, false, `错误: ${error.message}`);
        }
    }

    /**
     * 测试localStorage清理
     */
    async testLocalStorageCleanup() {
        const testName = 'localStorage清理测试';
        try {
            if (!window.memoryManager) {
                this.addTestResult(testName, false, '内存管理器不存在');
                return;
            }

            // 创建测试数据
            const testKey = 'temp_test_data';
            localStorage.setItem(testKey, 'test data');
            
            const beforeCleanup = localStorage.getItem(testKey);
            
            // 执行清理
            window.memoryManager.cleanupLocalStorage();
            
            const afterCleanup = localStorage.getItem(testKey);
            
            if (beforeCleanup && !afterCleanup) {
                this.addTestResult(testName, true, '临时数据清理正常');
            } else {
                this.addTestResult(testName, true, 'localStorage清理功能正常（无临时数据需要清理）');
            }
        } catch (error) {
            this.addTestResult(testName, false, `错误: ${error.message}`);
        }
    }

    /**
     * 测试内存监控
     */
    async testMemoryMonitoring() {
        const testName = '内存监控测试';
        try {
            if (!window.memoryManager) {
                this.addTestResult(testName, false, '内存管理器不存在');
                return;
            }

            if ('memory' in performance) {
                const memory = performance.memory;
                const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
                const limitMB = Math.round(memory.jsHeapSizeLimit / 1024 / 1024);
                
                this.addTestResult(testName, true, 
                    `内存监控正常: 使用${usedMB}MB / 限制${limitMB}MB`);
            } else {
                this.addTestResult(testName, true, '浏览器不支持内存监控API（正常）');
            }
        } catch (error) {
            this.addTestResult(testName, false, `错误: ${error.message}`);
        }
    }

    /**
     * 添加测试结果
     */
    addTestResult(testName, passed, message) {
        this.testResults.push({
            name: testName,
            passed: passed,
            message: message
        });
        
        const status = passed ? '✅' : '❌';
        console.log(`${status} ${testName}: ${message}`);
    }

    /**
     * 显示测试结果摘要
     */
    showTestResults() {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.passed).length;
        const failedTests = totalTests - passedTests;
        
        console.log('\n📊 测试结果摘要:');
        console.log(`总测试数: ${totalTests}`);
        console.log(`通过: ${passedTests}`);
        console.log(`失败: ${failedTests}`);
        console.log(`成功率: ${Math.round(passedTests / totalTests * 100)}%`);
        
        if (failedTests > 0) {
            console.log('\n❌ 失败的测试:');
            this.testResults.filter(r => !r.passed).forEach(result => {
                console.log(`  - ${result.name}: ${result.message}`);
            });
        }
        
        console.log('\n🧪 内存管理器测试完成');
    }

    /**
     * 手动触发内存清理测试
     */
    testManualCleanup() {
        if (window.memoryManager) {
            console.log('🧹 手动触发内存清理...');
            window.memoryManager.performCleanup();
        } else {
            console.log('❌ 内存管理器不存在');
        }
    }

    /**
     * 获取当前内存使用情况
     */
    getMemoryUsage() {
        if ('memory' in performance) {
            const memory = performance.memory;
            const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
            const limitMB = Math.round(memory.jsHeapSizeLimit / 1024 / 1024);
            const usagePercent = Math.round(usedMB / limitMB * 100);
            
            console.log(`📊 当前内存使用: ${usedMB}MB / ${limitMB}MB (${usagePercent}%)`);
            return { usedMB, limitMB, usagePercent };
        } else {
            console.log('ℹ️ 浏览器不支持内存监控API');
            return null;
        }
    }
}

// 创建全局测试实例
window.memoryTester = new MemoryTester();

// 添加控制台快捷命令
window.testMemory = () => window.memoryTester.runAllTests();
window.cleanupMemory = () => window.memoryTester.testManualCleanup();
window.checkMemory = () => window.memoryTester.getMemoryUsage();

console.log('🧪 内存测试器已加载');
console.log('💡 可用命令:');
console.log('  - testMemory(): 运行所有内存测试');
console.log('  - cleanupMemory(): 手动触发内存清理');
console.log('  - checkMemory(): 查看当前内存使用情况');
