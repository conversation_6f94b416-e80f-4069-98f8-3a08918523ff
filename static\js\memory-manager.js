/**
 * 内存管理器 - 解决白屏问题
 * 负责清理定时器、事件监听器和DOM元素，防止内存泄漏
 */

class MemoryManager {
    constructor() {
        this.timers = new Set();
        this.intervals = new Set();
        this.eventListeners = new Map();
        this.domObserver = null;
        this.maxMessages = 200; // 增加到200条消息，避免用户对话被中断
        this.maxLocalStorageSize = 10 * 1024 * 1024; // 增加到10MB
        this.cleanupInterval = null;
        this.isDestroyed = false;
        this.lastUserActivity = Date.now(); // 记录用户最后活动时间
        this.userActivityThreshold = 10 * 60 * 1000; // 10分钟无活动才清理

        this.startPeriodicCleanup();
        this.setupPageUnloadHandler();
        this.monitorMemoryUsage();
        this.trackUserActivity();
    }

    /**
     * 跟踪用户活动
     */
    trackUserActivity() {
        const updateActivity = () => {
            this.lastUserActivity = Date.now();
        };

        // 监听用户交互事件
        ['click', 'keypress', 'scroll', 'mousemove', 'touchstart'].forEach(event => {
            document.addEventListener(event, updateActivity, { passive: true });
        });
    }

    /**
     * 检查用户是否活跃
     */
    isUserActive() {
        const timeSinceLastActivity = Date.now() - this.lastUserActivity;
        return timeSinceLastActivity < this.userActivityThreshold;
    }

    /**
     * 启动定期清理
     */
    startPeriodicCleanup() {
        // 每10分钟检查一次，但只在用户不活跃时清理
        this.cleanupInterval = setInterval(() => {
            if (!this.isDestroyed) {
                this.smartCleanup();
            }
        }, 10 * 60 * 1000); // 改为10分钟

        this.intervals.add(this.cleanupInterval);
    }

    /**
     * 智能清理 - 考虑用户活动状态
     */
    smartCleanup() {
        // 如果用户正在活跃使用，只做轻量清理
        if (this.isUserActive()) {
            console.log('🔄 用户活跃中，执行轻量清理...');
            this.lightweightCleanup();
        } else {
            console.log('😴 用户不活跃，执行完整清理...');
            this.performCleanup();
        }
    }

    /**
     * 轻量清理 - 用户活跃时的温和清理
     */
    lightweightCleanup() {
        try {
            // 只清理明显的垃圾，不影响用户体验
            this.cleanupOrphanedElements();
            this.cleanupExpiredTimers();
            console.log('✅ 轻量清理完成');
        } catch (error) {
            console.error('❌ 轻量清理失败:', error);
        }
    }

    /**
     * 清理过期的定时器
     */
    cleanupExpiredTimers() {
        // 清理可能泄漏的定时器，但保留活跃的
        const now = Date.now();
        this.timers.forEach(timerId => {
            // 这里可以添加更智能的定时器检查逻辑
        });
    }

    /**
     * 执行清理操作
     */
    performCleanup() {
        console.log('🧹 开始执行内存清理...');
        
        try {
            this.cleanupOldMessages();
            this.cleanupLocalStorage();
            this.cleanupOrphanedElements();
            this.forceGarbageCollection();
            
            console.log('✅ 内存清理完成');
        } catch (error) {
            console.error('❌ 内存清理失败:', error);
        }
    }

    /**
     * 清理旧消息 - 更温和的策略
     */
    cleanupOldMessages() {
        const chatMessages = document.getElementById('chat-messages');
        if (!chatMessages) return;

        const messages = chatMessages.children;

        // 只有在消息数量明显过多时才清理
        if (messages.length > this.maxMessages) {
            // 如果用户活跃，保留更多消息
            const keepCount = this.isUserActive() ?
                Math.max(this.maxMessages * 0.8, 100) : // 活跃时保留80%或至少100条
                Math.max(this.maxMessages * 0.6, 50);   // 不活跃时保留60%或至少50条

            const removeCount = messages.length - keepCount;

            if (removeCount > 0) {
                console.log(`🗑️ 清理 ${removeCount} 条旧消息 (保留 ${keepCount} 条)`);

                // 温和地移除最旧的消息
                for (let i = 0; i < removeCount && messages[0]; i++) {
                    messages[0].remove();
                }
            }
        }
    }

    /**
     * 清理localStorage - 更保守的策略
     */
    cleanupLocalStorage() {
        try {
            const storageSize = this.getLocalStorageSize();
            if (storageSize > this.maxLocalStorageSize) {
                console.log('🗑️ localStorage 过大，开始温和清理...');

                // 清理对话历史（保留更多最近的对话）
                const historyKey = 'contentAuditor_conversationHistory';
                const history = JSON.parse(localStorage.getItem(historyKey) || '[]');

                // 根据用户活跃状态决定保留数量
                const keepCount = this.isUserActive() ? 50 : 30; // 活跃时保留50条，否则30条

                if (history.length > keepCount) {
                    const recentHistory = history.slice(-keepCount);
                    localStorage.setItem(historyKey, JSON.stringify(recentHistory));
                    console.log(`📝 对话历史已清理，保留最近 ${keepCount} 条`);
                }

                // 只清理明确的临时数据，不影响用户设置
                for (let key in localStorage) {
                    if (key.startsWith('temp_') ||
                        key.includes('cache_') ||
                        key.includes('_expired_') ||
                        (key.includes('session_') && !this.isUserActive())) {
                        localStorage.removeItem(key);
                    }
                }
            }
        } catch (error) {
            console.error('清理localStorage失败:', error);
        }
    }

    /**
     * 获取localStorage大小
     */
    getLocalStorageSize() {
        let total = 0;
        for (let key in localStorage) {
            if (localStorage.hasOwnProperty(key)) {
                total += localStorage[key].length + key.length;
            }
        }
        return total;
    }

    /**
     * 清理孤立的DOM元素
     */
    cleanupOrphanedElements() {
        // 清理没有父节点的进度元素
        const progressElements = document.querySelectorAll('[id^="progress_"], [id^="timer_"]');
        progressElements.forEach(element => {
            if (!element.parentNode || !document.contains(element)) {
                element.remove();
            }
        });

        // 清理空的消息容器
        const emptyMessages = document.querySelectorAll('.message:empty, .analysis-progress:empty');
        emptyMessages.forEach(element => element.remove());
    }

    /**
     * 强制垃圾回收（如果可用）
     */
    forceGarbageCollection() {
        if (window.gc && typeof window.gc === 'function') {
            window.gc();
        }
    }

    /**
     * 注册定时器
     */
    registerTimer(timerId) {
        this.timers.add(timerId);
        return timerId;
    }

    /**
     * 注册间隔器
     */
    registerInterval(intervalId) {
        this.intervals.add(intervalId);
        return intervalId;
    }

    /**
     * 清理定时器
     */
    clearTimer(timerId) {
        clearTimeout(timerId);
        this.timers.delete(timerId);
    }

    /**
     * 清理间隔器
     */
    clearInterval(intervalId) {
        clearInterval(intervalId);
        this.intervals.delete(intervalId);
    }

    /**
     * 注册事件监听器
     */
    registerEventListener(element, event, handler, options) {
        const key = `${element.constructor.name}_${event}`;
        if (!this.eventListeners.has(key)) {
            this.eventListeners.set(key, []);
        }
        
        this.eventListeners.get(key).push({
            element,
            event,
            handler,
            options
        });
        
        element.addEventListener(event, handler, options);
    }

    /**
     * 移除事件监听器
     */
    removeEventListener(element, event, handler) {
        element.removeEventListener(event, handler);
        
        const key = `${element.constructor.name}_${event}`;
        const listeners = this.eventListeners.get(key);
        if (listeners) {
            const index = listeners.findIndex(l => 
                l.element === element && 
                l.event === event && 
                l.handler === handler
            );
            if (index !== -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * 监控内存使用情况
     */
    monitorMemoryUsage() {
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
                const limitMB = Math.round(memory.jsHeapSizeLimit / 1024 / 1024);
                
                console.log(`📊 内存使用: ${usedMB}MB / ${limitMB}MB`);
                
                // 如果内存使用超过80%，执行紧急清理
                if (usedMB / limitMB > 0.8) {
                    console.warn('⚠️ 内存使用过高，执行紧急清理');
                    this.performEmergencyCleanup();
                }
            }, 60000); // 每分钟检查一次
        }
    }

    /**
     * 紧急清理 - 更温和的紧急处理
     */
    performEmergencyCleanup() {
        console.warn('⚠️ 执行紧急内存清理...');

        // 即使是紧急情况，也要考虑用户体验
        const chatMessages = document.getElementById('chat-messages');
        if (chatMessages && chatMessages.children.length > 50) {
            // 紧急情况下也保留至少50条消息
            const keepCount = this.isUserActive() ? 50 : 30;
            const removeCount = chatMessages.children.length - keepCount;

            console.log(`🚨 紧急清理 ${removeCount} 条消息，保留 ${keepCount} 条`);

            for (let i = 0; i < removeCount && chatMessages.children[0]; i++) {
                chatMessages.children[0].remove();
            }
        }

        // 清理缓存但保留重要数据
        this.cleanupLocalStorage();

        // 强制垃圾回收
        this.forceGarbageCollection();

        // 更温和的页面刷新提示
        if ('memory' in performance) {
            const memory = performance.memory;
            const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
            const limitMB = Math.round(memory.jsHeapSizeLimit / 1024 / 1024);

            if (usedMB / limitMB > 0.9) {
                console.warn('🔄 内存使用仍然过高，建议适时刷新页面');

                // 不立即弹窗，而是在控制台提示
                console.log('💡 建议：如果您的对话已告一段落，可以刷新页面以获得更好的性能');

                // 只有在用户不活跃时才考虑自动提示
                if (!this.isUserActive()) {
                    setTimeout(() => {
                        if (confirm('检测到内存使用较高，是否刷新页面以优化性能？\n（您的API密钥和设置将会保留）')) {
                            window.location.reload();
                        }
                    }, 5000); // 延迟5秒再提示
                }
            }
        }
    }

    /**
     * 设置页面卸载处理器
     */
    setupPageUnloadHandler() {
        const cleanup = () => {
            this.destroy();
        };

        window.addEventListener('beforeunload', cleanup);
        window.addEventListener('unload', cleanup);
        
        // 页面隐藏时也执行清理
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.performCleanup();
            }
        });
    }

    /**
     * 销毁内存管理器
     */
    destroy() {
        if (this.isDestroyed) return;
        
        console.log('🧹 销毁内存管理器...');
        this.isDestroyed = true;

        // 清理所有定时器
        this.timers.forEach(timerId => clearTimeout(timerId));
        this.intervals.forEach(intervalId => clearInterval(intervalId));

        // 清理所有事件监听器
        this.eventListeners.forEach(listeners => {
            listeners.forEach(({ element, event, handler }) => {
                try {
                    element.removeEventListener(event, handler);
                } catch (error) {
                    console.warn('清理事件监听器失败:', error);
                }
            });
        });

        // 清理DOM观察器
        if (this.domObserver) {
            this.domObserver.disconnect();
        }

        // 清空集合
        this.timers.clear();
        this.intervals.clear();
        this.eventListeners.clear();
        
        console.log('✅ 内存管理器已销毁');
    }
}

// 创建全局内存管理器实例
window.memoryManager = new MemoryManager();

console.log('📦 内存管理器已加载');
