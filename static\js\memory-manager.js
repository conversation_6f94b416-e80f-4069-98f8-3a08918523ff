/**
 * 内存管理器 - 解决白屏问题
 * 负责清理定时器、事件监听器和DOM元素，防止内存泄漏
 */

class MemoryManager {
    constructor() {
        this.timers = new Set();
        this.intervals = new Set();
        this.eventListeners = new Map();
        this.domObserver = null;
        this.maxMessages = 50; // 最大消息数量
        this.maxLocalStorageSize = 5 * 1024 * 1024; // 5MB
        this.cleanupInterval = null;
        this.isDestroyed = false;
        
        this.startPeriodicCleanup();
        this.setupPageUnloadHandler();
        this.monitorMemoryUsage();
    }

    /**
     * 启动定期清理
     */
    startPeriodicCleanup() {
        // 每5分钟执行一次清理
        this.cleanupInterval = setInterval(() => {
            if (!this.isDestroyed) {
                this.performCleanup();
            }
        }, 5 * 60 * 1000);
        
        this.intervals.add(this.cleanupInterval);
    }

    /**
     * 执行清理操作
     */
    performCleanup() {
        console.log('🧹 开始执行内存清理...');
        
        try {
            this.cleanupOldMessages();
            this.cleanupLocalStorage();
            this.cleanupOrphanedElements();
            this.forceGarbageCollection();
            
            console.log('✅ 内存清理完成');
        } catch (error) {
            console.error('❌ 内存清理失败:', error);
        }
    }

    /**
     * 清理旧消息
     */
    cleanupOldMessages() {
        const chatMessages = document.getElementById('chat-messages');
        if (!chatMessages) return;

        const messages = chatMessages.children;
        if (messages.length > this.maxMessages) {
            const removeCount = messages.length - this.maxMessages;
            console.log(`🗑️ 清理 ${removeCount} 条旧消息`);
            
            for (let i = 0; i < removeCount; i++) {
                if (messages[0]) {
                    messages[0].remove();
                }
            }
        }
    }

    /**
     * 清理localStorage
     */
    cleanupLocalStorage() {
        try {
            const storageSize = this.getLocalStorageSize();
            if (storageSize > this.maxLocalStorageSize) {
                console.log('🗑️ localStorage 过大，开始清理...');
                
                // 清理对话历史（保留最近的）
                const historyKey = 'contentAuditor_conversationHistory';
                const history = JSON.parse(localStorage.getItem(historyKey) || '[]');
                if (history.length > 20) {
                    const recentHistory = history.slice(-20);
                    localStorage.setItem(historyKey, JSON.stringify(recentHistory));
                }
                
                // 清理其他临时数据
                for (let key in localStorage) {
                    if (key.startsWith('temp_') || key.includes('cache_')) {
                        localStorage.removeItem(key);
                    }
                }
            }
        } catch (error) {
            console.error('清理localStorage失败:', error);
        }
    }

    /**
     * 获取localStorage大小
     */
    getLocalStorageSize() {
        let total = 0;
        for (let key in localStorage) {
            if (localStorage.hasOwnProperty(key)) {
                total += localStorage[key].length + key.length;
            }
        }
        return total;
    }

    /**
     * 清理孤立的DOM元素
     */
    cleanupOrphanedElements() {
        // 清理没有父节点的进度元素
        const progressElements = document.querySelectorAll('[id^="progress_"], [id^="timer_"]');
        progressElements.forEach(element => {
            if (!element.parentNode || !document.contains(element)) {
                element.remove();
            }
        });

        // 清理空的消息容器
        const emptyMessages = document.querySelectorAll('.message:empty, .analysis-progress:empty');
        emptyMessages.forEach(element => element.remove());
    }

    /**
     * 强制垃圾回收（如果可用）
     */
    forceGarbageCollection() {
        if (window.gc && typeof window.gc === 'function') {
            window.gc();
        }
    }

    /**
     * 注册定时器
     */
    registerTimer(timerId) {
        this.timers.add(timerId);
        return timerId;
    }

    /**
     * 注册间隔器
     */
    registerInterval(intervalId) {
        this.intervals.add(intervalId);
        return intervalId;
    }

    /**
     * 清理定时器
     */
    clearTimer(timerId) {
        clearTimeout(timerId);
        this.timers.delete(timerId);
    }

    /**
     * 清理间隔器
     */
    clearInterval(intervalId) {
        clearInterval(intervalId);
        this.intervals.delete(intervalId);
    }

    /**
     * 注册事件监听器
     */
    registerEventListener(element, event, handler, options) {
        const key = `${element.constructor.name}_${event}`;
        if (!this.eventListeners.has(key)) {
            this.eventListeners.set(key, []);
        }
        
        this.eventListeners.get(key).push({
            element,
            event,
            handler,
            options
        });
        
        element.addEventListener(event, handler, options);
    }

    /**
     * 移除事件监听器
     */
    removeEventListener(element, event, handler) {
        element.removeEventListener(event, handler);
        
        const key = `${element.constructor.name}_${event}`;
        const listeners = this.eventListeners.get(key);
        if (listeners) {
            const index = listeners.findIndex(l => 
                l.element === element && 
                l.event === event && 
                l.handler === handler
            );
            if (index !== -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * 监控内存使用情况
     */
    monitorMemoryUsage() {
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
                const limitMB = Math.round(memory.jsHeapSizeLimit / 1024 / 1024);
                
                console.log(`📊 内存使用: ${usedMB}MB / ${limitMB}MB`);
                
                // 如果内存使用超过80%，执行紧急清理
                if (usedMB / limitMB > 0.8) {
                    console.warn('⚠️ 内存使用过高，执行紧急清理');
                    this.performEmergencyCleanup();
                }
            }, 60000); // 每分钟检查一次
        }
    }

    /**
     * 紧急清理
     */
    performEmergencyCleanup() {
        // 清理更多消息
        const chatMessages = document.getElementById('chat-messages');
        if (chatMessages && chatMessages.children.length > 20) {
            const removeCount = chatMessages.children.length - 20;
            for (let i = 0; i < removeCount; i++) {
                if (chatMessages.children[0]) {
                    chatMessages.children[0].remove();
                }
            }
        }

        // 清理所有缓存
        this.cleanupLocalStorage();
        
        // 强制垃圾回收
        this.forceGarbageCollection();
        
        // 重新加载页面（最后手段）
        if ('memory' in performance) {
            const memory = performance.memory;
            const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
            const limitMB = Math.round(memory.jsHeapSizeLimit / 1024 / 1024);
            
            if (usedMB / limitMB > 0.9) {
                console.warn('🔄 内存使用仍然过高，建议刷新页面');
                if (confirm('内存使用过高，是否刷新页面以释放内存？')) {
                    window.location.reload();
                }
            }
        }
    }

    /**
     * 设置页面卸载处理器
     */
    setupPageUnloadHandler() {
        const cleanup = () => {
            this.destroy();
        };

        window.addEventListener('beforeunload', cleanup);
        window.addEventListener('unload', cleanup);
        
        // 页面隐藏时也执行清理
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.performCleanup();
            }
        });
    }

    /**
     * 销毁内存管理器
     */
    destroy() {
        if (this.isDestroyed) return;
        
        console.log('🧹 销毁内存管理器...');
        this.isDestroyed = true;

        // 清理所有定时器
        this.timers.forEach(timerId => clearTimeout(timerId));
        this.intervals.forEach(intervalId => clearInterval(intervalId));

        // 清理所有事件监听器
        this.eventListeners.forEach(listeners => {
            listeners.forEach(({ element, event, handler }) => {
                try {
                    element.removeEventListener(event, handler);
                } catch (error) {
                    console.warn('清理事件监听器失败:', error);
                }
            });
        });

        // 清理DOM观察器
        if (this.domObserver) {
            this.domObserver.disconnect();
        }

        // 清空集合
        this.timers.clear();
        this.intervals.clear();
        this.eventListeners.clear();
        
        console.log('✅ 内存管理器已销毁');
    }
}

// 创建全局内存管理器实例
window.memoryManager = new MemoryManager();

console.log('📦 内存管理器已加载');
