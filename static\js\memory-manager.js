/**
 * 内存管理器 - 解决白屏问题
 * 负责清理定时器、事件监听器和DOM元素，防止内存泄漏
 */

class MemoryManager {
    constructor() {
        this.timers = new Set();
        this.intervals = new Set();
        this.eventListeners = new Map();
        this.domObserver = null;
        this.maxMessages = 200; // 增加到200条消息，避免用户对话被中断
        this.maxLocalStorageSize = 10 * 1024 * 1024; // 增加到10MB
        this.cleanupInterval = null;
        this.isDestroyed = false;
        this.lastUserActivity = Date.now(); // 记录用户最后活动时间
        this.userActivityThreshold = 20 * 60 * 1000; // 20分钟无活动才清理

        this.startPeriodicCleanup();
        this.setupPageUnloadHandler();
        this.monitorMemoryUsage();
        this.trackUserActivity();
    }

    /**
     * 跟踪用户活动
     */
    trackUserActivity() {
        const updateActivity = () => {
            this.lastUserActivity = Date.now();
        };

        // 监听用户交互事件
        ['click', 'keypress', 'scroll', 'mousemove', 'touchstart'].forEach(event => {
            document.addEventListener(event, updateActivity, { passive: true });
        });
    }

    /**
     * 检查用户是否活跃
     */
    isUserActive() {
        const timeSinceLastActivity = Date.now() - this.lastUserActivity;
        return timeSinceLastActivity < this.userActivityThreshold;
    }

    /**
     * 启动定期清理
     */
    startPeriodicCleanup() {
        // 每40分钟检查一次，减少干扰
        this.cleanupInterval = setInterval(() => {
            if (!this.isDestroyed) {
                this.smartCleanup();
            }
        }, 40 * 60 * 1000); // 40分钟

        this.intervals.add(this.cleanupInterval);
    }

    /**
     * 智能清理 - 静默工作
     */
    smartCleanup() {
        // 静默清理，不打扰用户
        if (this.isUserActive()) {
            this.lightweightCleanup();
        } else {
            this.performCleanup();
        }
    }

    /**
     * 轻量清理 - 静默清理垃圾
     */
    lightweightCleanup() {
        try {
            this.cleanupOrphanedElements();
            this.cleanupExpiredTimers();
        } catch (error) {
            // 静默处理错误
        }
    }

    /**
     * 清理过期的定时器
     */
    cleanupExpiredTimers() {
        // 清理可能泄漏的定时器，但保留活跃的
        const now = Date.now();
        this.timers.forEach(timerId => {
            // 这里可以添加更智能的定时器检查逻辑
        });
    }

    /**
     * 执行清理操作 - 静默工作
     */
    performCleanup() {
        try {
            this.cleanupOldMessages();
            this.cleanupLocalStorage();
            this.cleanupOrphanedElements();
            this.forceGarbageCollection();
        } catch (error) {
            // 静默处理错误
        }
    }

    /**
     * 清理旧消息 - 更温和的策略
     */
    cleanupOldMessages() {
        const chatMessages = document.getElementById('chat-messages');
        if (!chatMessages) return;

        const messages = chatMessages.children;

        // 只有在消息数量明显过多时才清理
        if (messages.length > this.maxMessages) {
            // 如果用户活跃，保留更多消息
            const keepCount = this.isUserActive() ?
                Math.max(this.maxMessages * 0.8, 100) : // 活跃时保留80%或至少100条
                Math.max(this.maxMessages * 0.6, 50);   // 不活跃时保留60%或至少50条

            const removeCount = messages.length - keepCount;

            if (removeCount > 0) {
                // 静默移除最旧的消息
                for (let i = 0; i < removeCount && messages[0]; i++) {
                    messages[0].remove();
                }
            }
        }
    }

    /**
     * 清理localStorage - 更保守的策略
     */
    cleanupLocalStorage() {
        try {
            const storageSize = this.getLocalStorageSize();
            if (storageSize > this.maxLocalStorageSize) {

                // 清理对话历史（保留更多最近的对话）
                const historyKey = 'contentAuditor_conversationHistory';
                const history = JSON.parse(localStorage.getItem(historyKey) || '[]');

                // 根据用户活跃状态决定保留数量
                const keepCount = this.isUserActive() ? 50 : 30; // 活跃时保留50条，否则30条

                if (history.length > keepCount) {
                    const recentHistory = history.slice(-keepCount);
                    localStorage.setItem(historyKey, JSON.stringify(recentHistory));
                }

                // 只清理明确的临时数据，不影响用户设置
                for (let key in localStorage) {
                    if (key.startsWith('temp_') ||
                        key.includes('cache_') ||
                        key.includes('_expired_') ||
                        (key.includes('session_') && !this.isUserActive())) {
                        localStorage.removeItem(key);
                    }
                }
            }
        } catch (error) {
            // 静默处理错误
        }
    }

    /**
     * 获取localStorage大小
     */
    getLocalStorageSize() {
        let total = 0;
        for (let key in localStorage) {
            if (localStorage.hasOwnProperty(key)) {
                total += localStorage[key].length + key.length;
            }
        }
        return total;
    }

    /**
     * 清理孤立的DOM元素
     */
    cleanupOrphanedElements() {
        // 清理没有父节点的进度元素
        const progressElements = document.querySelectorAll('[id^="progress_"], [id^="timer_"]');
        progressElements.forEach(element => {
            if (!element.parentNode || !document.contains(element)) {
                element.remove();
            }
        });

        // 清理空的消息容器
        const emptyMessages = document.querySelectorAll('.message:empty, .analysis-progress:empty');
        emptyMessages.forEach(element => element.remove());
    }

    /**
     * 强制垃圾回收（如果可用）
     */
    forceGarbageCollection() {
        if (window.gc && typeof window.gc === 'function') {
            window.gc();
        }
    }

    /**
     * 注册定时器
     */
    registerTimer(timerId) {
        this.timers.add(timerId);
        return timerId;
    }

    /**
     * 注册间隔器
     */
    registerInterval(intervalId) {
        this.intervals.add(intervalId);
        return intervalId;
    }

    /**
     * 清理定时器
     */
    clearTimer(timerId) {
        clearTimeout(timerId);
        this.timers.delete(timerId);
    }

    /**
     * 清理间隔器
     */
    clearInterval(intervalId) {
        clearInterval(intervalId);
        this.intervals.delete(intervalId);
    }

    /**
     * 注册事件监听器
     */
    registerEventListener(element, event, handler, options) {
        const key = `${element.constructor.name}_${event}`;
        if (!this.eventListeners.has(key)) {
            this.eventListeners.set(key, []);
        }
        
        this.eventListeners.get(key).push({
            element,
            event,
            handler,
            options
        });
        
        element.addEventListener(event, handler, options);
    }

    /**
     * 移除事件监听器
     */
    removeEventListener(element, event, handler) {
        element.removeEventListener(event, handler);
        
        const key = `${element.constructor.name}_${event}`;
        const listeners = this.eventListeners.get(key);
        if (listeners) {
            const index = listeners.findIndex(l => 
                l.element === element && 
                l.event === event && 
                l.handler === handler
            );
            if (index !== -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * 监控内存使用情况 - 静默监控
     */
    monitorMemoryUsage() {
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
                const limitMB = Math.round(memory.jsHeapSizeLimit / 1024 / 1024);

                // 如果内存使用超过85%，执行紧急清理
                if (usedMB / limitMB > 0.85) {
                    this.performEmergencyCleanup();
                }
            }, 120000); // 每2分钟检查一次
        }
    }

    /**
     * 紧急清理 - 静默处理
     */
    performEmergencyCleanup() {
        // 静默紧急清理
        const chatMessages = document.getElementById('chat-messages');
        if (chatMessages && chatMessages.children.length > 50) {
            const keepCount = this.isUserActive() ? 50 : 30;
            const removeCount = chatMessages.children.length - keepCount;

            for (let i = 0; i < removeCount && chatMessages.children[0]; i++) {
                chatMessages.children[0].remove();
            }
        }

        this.cleanupLocalStorage();
        this.forceGarbageCollection();

        // 只在极端情况下才考虑刷新提示
        if ('memory' in performance) {
            const memory = performance.memory;
            const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
            const limitMB = Math.round(memory.jsHeapSizeLimit / 1024 / 1024);

            // 只有在内存使用超过95%且用户不活跃时才提示
            if (usedMB / limitMB > 0.95 && !this.isUserActive()) {
                setTimeout(() => {
                    if (confirm('系统检测到内存使用过高，建议刷新页面以获得更好的性能。\n您的设置和API密钥将会保留。')) {
                        window.location.reload();
                    }
                }, 10000); // 延迟10秒再提示
            }
        }
    }

    /**
     * 设置页面卸载处理器
     */
    setupPageUnloadHandler() {
        const cleanup = () => {
            this.destroy();
        };

        window.addEventListener('beforeunload', cleanup);
        window.addEventListener('unload', cleanup);
        
        // 页面隐藏时也执行清理
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.performCleanup();
            }
        });
    }

    /**
     * 销毁内存管理器
     */
    destroy() {
        if (this.isDestroyed) return;

        this.isDestroyed = true;

        // 清理所有定时器
        this.timers.forEach(timerId => clearTimeout(timerId));
        this.intervals.forEach(intervalId => clearInterval(intervalId));

        // 清理所有事件监听器
        this.eventListeners.forEach(listeners => {
            listeners.forEach(({ element, event, handler }) => {
                try {
                    element.removeEventListener(event, handler);
                } catch (error) {
                    // 静默处理
                }
            });
        });

        // 清理DOM观察器
        if (this.domObserver) {
            this.domObserver.disconnect();
        }

        // 清空集合
        this.timers.clear();
        this.intervals.clear();
        this.eventListeners.clear();
    }
}

// 创建全局内存管理器实例
window.memoryManager = new MemoryManager();
