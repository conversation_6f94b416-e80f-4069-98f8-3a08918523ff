# 内容审核管家 - 魔搭创空间使用指南

## 🎯 专为魔搭创空间优化

本项目已针对魔搭创空间的使用场景进行了深度优化，确保长时间稳定运行，避免白屏问题。

## ✨ 核心优化特性

### 🧠 智能内存管理
- **用户活跃检测**：自动识别您是否正在使用
- **动态清理策略**：活跃时保护对话，空闲时释放内存
- **温和清理**：不会突然中断您的对话

### 💬 对话保护机制
- **长对话支持**：活跃用户可保留200条消息
- **历史记录**：自动保存最近50条对话历史
- **无感清理**：只在您不使用时进行深度清理

### 📊 实时监控
- **内存使用监控**：自动监控浏览器内存使用
- **智能预警**：内存过高时温和提示
- **性能优化**：自动优化运行性能

## 🚀 使用建议

### 💡 最佳实践
1. **长时间使用**：可以放心进行长时间对话，系统会智能保护您的内容
2. **定期休息**：偶尔离开几分钟，系统会自动优化性能
3. **关注提示**：如果控制台提示内存使用较高，可在对话告一段落时刷新页面

### 🔍 监控方法
打开浏览器控制台（F12），您会看到：
```
📦 内存管理器已加载
🔄 用户活跃中，执行轻量清理...
📊 内存使用: 45MB / 512MB
```

### ⚠️ 注意事项
- **API密钥**：刷新页面后API密钥会保留
- **对话历史**：重要对话会自动保存
- **设置保留**：主题、模式等设置会保持

## 🛠️ 功能说明

### 智能清理策略
| 用户状态 | 消息保留 | 历史记录 | 清理频率 | 清理程度 |
|---------|---------|---------|---------|---------|
| 活跃使用 | 200条 | 50条 | 10分钟 | 轻量清理 |
| 空闲状态 | 100条 | 30条 | 10分钟 | 完整清理 |
| 紧急情况 | 50条 | 20条 | 立即 | 温和清理 |

### 用户活跃检测
系统会监听以下活动：
- 🖱️ 鼠标点击和移动
- ⌨️ 键盘输入
- 📜 页面滚动
- 📱 触摸操作

10分钟内有任何活动即视为活跃状态。

## 🧪 测试功能

### 控制台命令
您可以在浏览器控制台中使用以下命令：

```javascript
// 测试内存管理功能
testMemory()

// 查看当前内存使用
checkMemory()

// 手动清理内存（如果需要）
cleanupMemory()
```

### 测试结果示例
```
🧪 开始内存管理器测试...
✅ 内存管理器实例检查: 内存管理器实例存在
✅ 定时器管理测试: 定时器注册和清理正常
✅ 间隔器管理测试: 间隔器注册和清理正常
✅ 内存清理功能测试: 消息清理测试完成 (温和策略)
✅ localStorage清理测试: 临时数据清理正常
✅ 内存监控测试: 内存监控正常: 使用45MB / 限制512MB

📊 测试结果摘要:
总测试数: 6
通过: 6
失败: 0
成功率: 100%
```

## 🔧 故障排除

### 如果仍然出现问题

1. **检查控制台**
   ```javascript
   // 查看是否有错误信息
   console.log('检查内存管理器状态')
   testMemory()
   ```

2. **手动清理**
   ```javascript
   // 如果感觉卡顿，可以手动清理
   cleanupMemory()
   ```

3. **查看内存使用**
   ```javascript
   // 查看当前内存状态
   checkMemory()
   ```

### 常见问题

**Q: 我的对话会被删除吗？**
A: 不会！系统会智能检测您的使用状态，只在您不活跃时才清理旧消息，且会保留足够的对话内容。

**Q: 多长时间会清理一次？**
A: 系统每10分钟检查一次，但会根据您的活跃状态决定清理程度。活跃时只做轻量清理，不影响使用。

**Q: 如何知道系统在清理？**
A: 控制台会显示清理日志，如"🔄 用户活跃中，执行轻量清理..."，您可以放心继续使用。

**Q: 内存使用过高怎么办？**
A: 系统会自动处理，如果确实需要，会在控制台温和提示，不会强制中断您的使用。

## 📈 性能优化建议

### 日常使用
- ✅ 正常使用无需特别注意，系统会自动优化
- ✅ 长时间对话完全支持，不用担心被中断
- ✅ 偶尔查看控制台了解系统状态

### 最佳性能
- 🔄 每4-6小时刷新一次页面（可选）
- 💾 重要对话可以手动复制保存
- 🧹 感觉卡顿时可运行 `cleanupMemory()`

## 📞 技术支持

如果遇到问题，请提供：
- 浏览器类型和版本
- 控制台错误信息（如有）
- 使用时长和操作记录
- 内存测试结果（运行 `testMemory()`）

---

**享受您的内容审核体验！** 🎉

系统已经过专门优化，可以放心进行长时间的内容审核工作。
